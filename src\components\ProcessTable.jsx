import React, { useState, useEffect } from 'react';
import { Tag } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import { useSocketStore } from '../utils/socket';
import { PROCESS_COLORS } from '../utils/constants';

const ProcessTable = ({ processType, processName }) => {
  const { processData } = useSocketStore();

  // 获取流程对应的主题色
  const processColor = PROCESS_COLORS[processType] || '#409EFF';

  // 格式化时间戳
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return timestamp;

    // 检查是否已经是格式化后的字符串
    if (typeof timestamp === 'string' && !timestamp.includes('T')) {
      return timestamp;
    }

    // 转换ISO格式为标准格式
    const date = new Date(timestamp);
    return date.getFullYear() + '-' +
          String(date.getMonth() + 1).padStart(2, '0') + '-' +
          String(date.getDate()).padStart(2, '0') + ' ' +
          String(date.getHours()).padStart(2, '0') + ':' +
          String(date.getMinutes()).padStart(2, '0') + ':' +
          String(date.getSeconds()).padStart(2, '0');
  };

  // 准备数据源
  const getDataSource = () => {
    const currentProcessData = processData[processType] || [];

    // 按时间戳降序排序（最新的记录在前）
    return [...currentProcessData]
      .sort((a, b) => {
        const timeA = new Date(a.timestamp).getTime();
        const timeB = new Date(b.timestamp).getTime();
        return timeB - timeA; // 降序排序
      })
      .map(item => ({
        ...item,
        timestamp: formatTimestamp(item.timestamp),
        key: `${item.batch_id}_${item.timestamp}`
      }));
  };

  // 表格列定义
  const columns = [
    {
      title: '客户代码',
      dataIndex: 'company',
      key: 'company',
      width: 150,
      ellipsis: true,
    },
    {
      title: '批次ID',
      dataIndex: 'batch_id',
      key: 'batch_id',
      width: 120,
    },
    {
      title: '流程',
      dataIndex: 'process',
      key: 'process',
      width: 80,
      align: 'center',
      render: () => (
        <Tag color={processColor} style={{ fontWeight: 'bold' }}>
          {processName}
        </Tag>
      ),
    },
    {
      title: '操作时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      valueType: 'dateTime',
    },
    {
      title: '员工',
      dataIndex: 'employee',
      key: 'employee',
      width: 120,
    },
  ];

  return (
    <ProTable
      headerTitle={`${processName}流程数据`}
      rowKey="key"
      search={false}
      options={{
        density: true,
        fullScreen: true,
        reload: false,
        setting: true,
      }}
      pagination={{
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条记录`,
        pageSizeOptions: ['10', '20', '50', '100'],
        defaultPageSize: 10,
      }}
      dataSource={getDataSource()}
      columns={columns}
      dateFormatter="string"
      toolbar={{
        title: (
          <div style={{
            borderLeft: `4px solid ${processColor}`,
            paddingLeft: '10px',
            fontWeight: 'bold'
          }}>
            {processName}流程数据
          </div>
        ),
      }}
    />
  );
};

export default ProcessTable;

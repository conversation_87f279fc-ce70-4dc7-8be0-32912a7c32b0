import { io } from 'socket.io-client';
import { create } from 'zustand';

// 创建状态管理store
export const useSocketStore = create((set) => ({
  connectionStatus: 'disconnected',
  processData: {
    storage: [],
    film: [],
    cutting: [],
    inspection: [],
    shipping: []
  },
  recentBatches: [],
  processCounts: {},
  lastUpdateTime: '--',

  // 更新连接状态
  setConnectionStatus: (status) => set({ connectionStatus: status }),

  // 更新流程数据
  setProcessData: (processType, data) => set((state) => ({
    processData: {
      ...state.processData,
      [processType]: data
    }
  })),

  // 更新最近批次
  setRecentBatches: (data) => set({ recentBatches: data }),

  // 更新流程计数
  setProcessCounts: (data) => set({ processCounts: data }),

  // 更新最后更新时间
  setLastUpdateTime: (time) => set({ lastUpdateTime: time }),

  // 格式化当前时间
  formatCurrentTime: () => {
    const now = new Date();
    return `北京时间：${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  }
}));

// 格式化日期时间为标准格式
function formatDateTime(timestamp) {
  if (!timestamp) return timestamp;

  // 检查是否已经是格式化后的字符串
  if (typeof timestamp === 'string' && !timestamp.includes('T')) {
    return timestamp;
  }

  // 转换ISO格式为标准格式
  const date = new Date(timestamp);
  return date.getFullYear() + '-' +
         String(date.getMonth() + 1).padStart(2, '0') + '-' +
         String(date.getDate()).padStart(2, '0') + ' ' +
         String(date.getHours()).padStart(2, '0') + ':' +
         String(date.getMinutes()).padStart(2, '0') + ':' +
         String(date.getSeconds()).padStart(2, '0');
}

// 深度格式化对象中的所有日期属性
function deepFormatDates(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => deepFormatDates(item));
  } else if (obj !== null && typeof obj === 'object') {
    const result = {};
    for (const key in obj) {
      if (key === 'timestamp' || key.endsWith('_at') || key.endsWith('_time')) {
        result[key] = formatDateTime(obj[key]);
      } else {
        result[key] = deepFormatDates(obj[key]);
      }
    }
    return result;
  }
  return obj;
}

// 创建Socket实例 - 使用相对路径，通过Vite代理转发
const socket = io('/', {
  autoConnect: false,
  reconnection: true,
  reconnectionAttempts: Infinity,
  withCredentials: false, // 禁用凭证，解决CORS问题
  transports: ['websocket', 'polling'], // 尝试先使用WebSocket，然后回退到polling
  path: '/socket.io' // 确保路径正确
});

// 连接WebSocket
export const connectSocket = () => {
  if (socket.disconnected) {
    socket.connect();
  }
};

// 断开WebSocket连接
export const disconnectSocket = () => {
  if (socket.connected) {
    socket.disconnect();
  }
};

// 设置事件监听器
export const setupSocketListeners = () => {
  const {
    setConnectionStatus,
    setProcessData,
    setRecentBatches,
    setProcessCounts,
    setLastUpdateTime,
    formatCurrentTime
  } = useSocketStore.getState();

  // 连接状态监听
  socket.on('connect', () => {
    setConnectionStatus('connected');
    console.log('WebSocket连接成功');
  });

  socket.on('disconnect', () => {
    setConnectionStatus('disconnected');
    console.log('WebSocket连接断开');
  });

  socket.on('connect_error', (error) => {
    setConnectionStatus('error');
    console.error('WebSocket连接错误:', error);
  });

  // 流程数据更新监听
  socket.on('process_update', (data) => {
    const { processType, records } = data;
    if (processType && records) {
      // 确保时间格式正确
      const formattedRecords = deepFormatDates(records);
      setProcessData(processType, formattedRecords);
      setLastUpdateTime(formatCurrentTime());
    }
  });

  // 最近更新的批次
  socket.on('recent_batches', (data) => {
    // 确保时间格式正确
    setRecentBatches(deepFormatDates(data));
    setLastUpdateTime(formatCurrentTime());
  });

  // 今日统计数据更新
  socket.on('process_counts', (data) => {
    setProcessCounts(data);
    setLastUpdateTime(formatCurrentTime());
  });
};

// 请求初始数据
export const requestInitialData = () => {
  if (socket.connected) {
    socket.emit('get_all_data');
  }
};

// 提供单例模式的Socket实例
export default {
  socket,
  connectSocket,
  disconnectSocket,
  setupSocketListeners,
  requestInitialData
};
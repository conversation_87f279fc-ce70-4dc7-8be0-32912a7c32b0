import React, { useEffect, useState, useCallback } from 'react';
import { Card, Row, Col, Table, Tag, Progress, Typography, Skeleton, Modal, Button, message, Popconfirm } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { deleteCompanyData } from '../utils/api';
import { useSocketStore } from '../utils/socket';
import { PROCESS_TYPES, PROCESS_COLORS } from '../utils/constants';

const { Title, Text } = Typography;

const Dashboard = ({ showCompletionRates = false }) => {
  const {
    processData,
    recentBatches,
    processCounts
  } = useSocketStore();

  const [companyCompletionRates, setCompanyCompletionRates] = useState({});

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState('');
  const [companyBatchesData, setCompanyBatchesData] = useState([]);

  // 计算全流程完成数量
  const completedCount = processCounts.shipping || 0;

  // 获取流程显示名称
  const getProcessLabel = (processType) => {
    // 如果已经是中文名称，直接返回
    const chineseProcessNames = ['入库', '贴膜', '切割', '检验', '出货'];
    if (chineseProcessNames.includes(processType)) {
      return processType;
    }

    // 否则尝试查找对应的标签
    const process = PROCESS_TYPES.find(p => p.value === processType);
    return process ? process.label : processType;
  };

  // 获取流程类型的键名
  const getProcessKey = (row) => {
    // 从多个可能的属性中获取流程类型
    const processType = row.processName || row.process_type || '';

    // 将中文流程名称映射到英文键名
    if (processType === '入库') {
      return 'storage';
    } else if (processType === '贴膜') {
      return 'film';
    } else if (processType === '切割') {
      return 'cutting';
    } else if (processType === '检验') {
      return 'inspection';
    } else if (processType === '出货') {
      return 'shipping';
    } else {
      // 尝试使用原始值作为键名
      return processType;
    }
  };

  // 进度圈渐变色配置
  const getProgressGradientColors = (percentage) => {
    if (percentage < 30) {
      // 红色渐变
      return {
        '0%': '#FF4D4F',
        '100%': '#F56C6C'
      };
    } else if (percentage < 70) {
      // 黄色渐变
      return {
        '0%': '#FAAD14',
        '100%': '#E6A23C'
      };
    } else {
      // 绿色渐变
      return {
        '0%': '#52C41A',
        '100%': '#67C23A'
      };
    }
  };

  // 获取进度圈文本颜色
  const getProgressTextColor = (percentage) => {
    if (percentage < 30) return '#F56C6C';  // 红色
    if (percentage < 70) return '#E6A23C';  // 黄色
    return '#67C23A';  // 绿色
  };

  // 计算各公司批次完成率
  const calculateCompanyCompletionRates = useCallback(() => {
    const allCompanies = new Set();
    const companyBatches = {};
    const companyCompletedBatches = {};

    // 收集所有公司和批次信息
    for (const processType of Object.keys(processData)) {
      if (Array.isArray(processData[processType])) {
        processData[processType].forEach(record => {
          if (record.company) {
            allCompanies.add(record.company);

            // 初始化公司批次计数
            if (!companyBatches[record.company]) {
              companyBatches[record.company] = new Set();
              companyCompletedBatches[record.company] = new Set();
            }

            // 添加批次ID到该公司的集合中
            if (record.batch_id) {
              companyBatches[record.company].add(record.batch_id);

              // 如果是出货流程，则添加到已完成批次集合
              if (processType === 'shipping') {
                companyCompletedBatches[record.company].add(record.batch_id);
              }
            }
          }
        });
      }
    }

    // 计算各公司的完成率
    const rates = {};
    allCompanies.forEach(company => {
      const total = companyBatches[company].size;
      const completed = companyCompletedBatches[company].size;
      const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

      rates[company] = {
        total,
        completed,
        percentage
      };
    });

    // 更新排序逻辑：未完成100%的公司优先，已完成100%的排在后面
    // 在各分组内部，按照完成率从高到低排序
    const sortedRates = Object.fromEntries(
      Object.entries(rates).sort((a, b) => {
        // 如果一个是100%完成而另一个不是，优先展示未完成的
        if (a[1].percentage === 100 && b[1].percentage < 100) return 1;
        if (a[1].percentage < 100 && b[1].percentage === 100) return -1;

        // 在相同分组内(都是100%或都不是100%)，按照完成率从高到低排序
        return b[1].percentage - a[1].percentage;
      })
    );

    setCompanyCompletionRates(sortedRates);
  }, [processData]);

  // 表格列定义
  const columns = [
    {
      title: '客户代码',
      dataIndex: 'company',
      key: 'company',
      width: 150,
    },
    {
      title: '批次ID',
      dataIndex: 'batchId',
      key: 'batchId',
      width: 120,
    },
    {
      title: '流程',
      dataIndex: 'processName',
      key: 'processName',
      width: 80,
      align: 'center',
      render: (_, record) => {
        const processKey = getProcessKey(record);
        return (
          <Tag color={PROCESS_COLORS[processKey]} style={{ fontWeight: 'bold' }}>
            {getProcessLabel(record.processName || record.process_type)}
          </Tag>
        );
      },
    },
    {
      title: '操作时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 150,
    },
    {
      title: '员工',
      dataIndex: 'employee',
      key: 'employee',
      width: 100,
    },
  ];

  // 移除了分页相关的函数

  // 处理公司批次数据的函数
  const processCompanyBatchesData = useCallback((company) => {
    // 获取该公司的所有批次ID
    const batchIds = new Set();

    // 从各个流程中收集该公司的批次ID
    Object.keys(processData).forEach(processType => {
      if (Array.isArray(processData[processType])) {
        processData[processType].forEach(record => {
          if (record.company === company && record.batch_id) {
            batchIds.add(record.batch_id);
          }
        });
      }
    });

    // 为每个批次收集各流程的状态
    const batchesData = Array.from(batchIds).map(batchId => {
      const batchData = {
        key: batchId,
        batch_id: batchId,
        storage: null,
        film: null,
        cutting: null,
        inspection: null,
        shipping: null,
        progress: 0
      };

      // 检查每个流程的状态
      Object.keys(processData).forEach(processType => {
        if (Array.isArray(processData[processType])) {
          const record = processData[processType].find(r => r.batch_id === batchId && r.company === company);
          if (record) {
            batchData[processType] = record;
          }
        }
      });

      // 计算进度
      const completedSteps = Object.keys(processData).filter(processType => batchData[processType] !== null).length;
      batchData.progress = Math.round((completedSteps / 5) * 100);

      return batchData;
    });

    return batchesData;
  }, [processData]);

  // 点击进度圈的处理函数
  const handleProgressClick = (company) => {
    setSelectedCompany(company);
    const batchesData = processCompanyBatchesData(company);
    setCompanyBatchesData(batchesData);
    setModalVisible(true);
  };

  // 删除客户代码的处理函数
  const handleDeleteCompany = async (company) => {
    try {
      const response = await deleteCompanyData(company);

      if (response.data && response.data.success) {
        message.success(`客户代码 ${company} 的所有数据已删除`);
        // 重新计算完成率，更新UI
        calculateCompanyCompletionRates();
        // 关闭模态框
        setModalVisible(false);
      } else {
        message.error(`删除失败: ${response.data?.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('删除客户代码数据失败:', error);
      message.error(`删除失败: ${error.response?.data?.message || error.message}`);
    }
  };

  // 渲染流程状态的函数
  const renderProcessStatus = (record, processType) => {
    if (!record) {
      return <Tag color="default">未开始</Tag>;
    }

    return (
      <div>
        <Tag color={PROCESS_COLORS[processType]} style={{ marginBottom: '4px' }}>已完成</Tag>
        <div style={{ fontSize: '12px', color: '#666' }}>
          <div>{record.timestamp}</div>
          <div>{record.employee}</div>
        </div>
      </div>
    );
  };

  // 组件挂载时加载数据
  useEffect(() => {
    // 初始计算完成率
    calculateCompanyCompletionRates();
  }, [processData, calculateCompanyCompletionRates]);

  return (
    <Card style={{ overflow: 'hidden' }}>
      {!showCompletionRates && (
        <div className="dashboard-stats">
          <Row gutter={20}>
            {PROCESS_TYPES.map(process => (
              <Col span={4} key={process.value}>
                <Card
                  styles={{ body: { padding: '10px' } }}
                  style={{ borderLeft: `4px solid ${PROCESS_COLORS[process.value]}` }}
                >
                  <div style={{ textAlign: 'center' }}>
                    <Title level={5} style={{ margin: 0 }}>{process.label}</Title>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', margin: '10px 0' }}>
                      {processCounts[process.value] || 0}
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>完成数量</Text>
                  </div>
                </Card>
              </Col>
            ))}

            <Col span={4}>
              <Card
                styles={{ body: { padding: '10px' } }}
                style={{ borderLeft: '4px solid #8e44ad' }}
              >
                <div style={{ textAlign: 'center' }}>
                  <Title level={5} style={{ margin: 0 }}>全流程完成</Title>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', margin: '10px 0' }}>
                    {completedCount}
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>完成批次</Text>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      )}

      {showCompletionRates && (
        <div style={{ marginTop: '10px', overflowY: 'auto', overflowX: 'hidden', maxHeight: 'calc(100vh - 180px)' }}>
          {Object.keys(companyCompletionRates).length === 0 ? (
            <Skeleton active paragraph={{ rows: 3 }} />
          ) : (
            <Row gutter={[12, 16]}>
              {Object.entries(companyCompletionRates).map(([company, rate]) => (
                <Col xs={24} sm={12} md={8} lg={4} xl={4} xxl={4} key={company}>
                  <Card styles={{ body: { padding: '16px 12px' } }} style={{ height: '100%' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <div style={{ fontWeight: 'bold', marginBottom: '16px', fontSize: '16px', textAlign: 'center', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center', overflow: 'hidden', textOverflow: 'ellipsis', width: '100%' }}>{company}</div>
                      <Progress
                        type="circle"
                        percent={rate.percentage}
                        strokeColor={getProgressGradientColors(rate.percentage)}
                        format={percent => (
                          <span style={{ color: getProgressTextColor(rate.percentage), fontSize: '24px' }}>
                            {percent}%
                          </span>
                        )}
                        size={160}
                        onClick={() => handleProgressClick(company)}
                        style={{ cursor: 'pointer' }}
                      />
                      <div style={{ marginTop: '16px', fontSize: '14px', color: '#606266', textAlign: 'center' }}>
                        <div style={{ whiteSpace: 'nowrap' }}>
                          <span>已完成</span>
                          <span style={{ fontWeight: 'bold', margin: '0 3px' }}>{rate.completed}/{rate.total}</span>
                        </div>
                        {rate.percentage === 100 && (
                          <div style={{ color: '#67c23a', fontWeight: 'bold', fontSize: '11px', marginTop: '3px' }}>
                            (已全部完成)
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </div>
      )}

      {!showCompletionRates && (
        <div style={{ marginTop: '20px' }}>
          <Title level={4}>最近更新的批次</Title>
          <Table
            dataSource={recentBatches}
            columns={columns}
            rowKey={(record) => {
              // 确保即使某些字段为undefined，也能生成唯一的键值
              const batchId = record.batch_id || 'no-batch';
              const processType = record.process_type || record.processName || 'no-process';
              const timestamp = record.timestamp || 'no-time';
              // 使用随机数来确保唯一性，而不依赖已弃用的index参数
              const randomId = Math.random().toString(36).substring(2, 10);
              return `${batchId}_${processType}_${timestamp}_${randomId}`;
            }}
            pagination={false} // 移除分页功能
          />
        </div>
      )}
      {/* 批次流程进展模态框 */}
      <Modal
        title={`${selectedCompany} 批次流程进展`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={1000}
        footer={
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Popconfirm
              title="删除客户代码"
              description={`确定要删除客户代码 ${selectedCompany} 的所有数据吗？此操作不可恢复。`}
              onConfirm={() => handleDeleteCompany(selectedCompany)}
              okText="删除"
              cancelText="取消"
              okButtonProps={{ danger: true }}
            >
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
              >
                删除客户代码
              </Button>
            </Popconfirm>
            <Button onClick={() => setModalVisible(false)}>关闭</Button>
          </div>
        }
      >
        <Table
          dataSource={companyBatchesData}
          columns={[
            {
              title: '批次ID',
              dataIndex: 'batch_id',
              key: 'batch_id',
              width: 120,
            },
            {
              title: '入库',
              dataIndex: 'storage',
              key: 'storage',
              width: 150,
              render: (record) => renderProcessStatus(record, 'storage'),
            },
            {
              title: '贴膜',
              dataIndex: 'film',
              key: 'film',
              width: 150,
              render: (record) => renderProcessStatus(record, 'film'),
            },
            {
              title: '切割',
              dataIndex: 'cutting',
              key: 'cutting',
              width: 150,
              render: (record) => renderProcessStatus(record, 'cutting'),
            },
            {
              title: '检验',
              dataIndex: 'inspection',
              key: 'inspection',
              width: 150,
              render: (record) => renderProcessStatus(record, 'inspection'),
            },
            {
              title: '出货',
              dataIndex: 'shipping',
              key: 'shipping',
              width: 150,
              render: (record) => renderProcessStatus(record, 'shipping'),
            },
            {
              title: '完成进度',
              dataIndex: 'progress',
              key: 'progress',
              width: 150,
              render: (progress) => (
                <Progress
                  percent={progress}
                  size="small"
                  status={progress === 100 ? 'success' : 'active'}
                />
              ),
            },
          ]}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Modal>
    </Card>
  );
};

export default Dashboard;

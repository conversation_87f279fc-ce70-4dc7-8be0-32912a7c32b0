const processModel = require('../models/process.model');
const moment = require('moment');

// 保存io实例的引用
let ioInstance = null;

// 保存已连接的客户端
const connectedClients = new Set();

// 各流程类型数据的缓存
const processDataCache = {
  storage: [],
  film: [],
  cutting: [],
  inspection: [],
  shipping: []
};

// 最近更新批次缓存
let recentBatchesCache = [];

// 今日各流程完成数量
let processCountsCache = {};

// 格式化日期时间，确保返回字符串格式
function formatDateTime(timestamp) {
  if (typeof timestamp === 'string' && 
      /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timestamp)) {
    return timestamp;
  }
  
  return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
}

// 深度格式化对象中的所有日期属性
function deepFormatDates(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => deepFormatDates(item));
  } else if (obj !== null && typeof obj === 'object') {
    const result = {};
    for (const key in obj) {
      if (key === 'timestamp' || key.endsWith('_at')) {
        result[key] = formatDateTime(obj[key]);
      } else {
        result[key] = deepFormatDates(obj[key]);
      }
    }
    return result;
  }
  return obj;
}

// 初始化Socket.IO
exports.init = (io) => {
  // 保存io实例以供后续使用
  ioInstance = io;

  // 添加Socket.IO中间件，拦截所有传出消息
  io.use((socket, next) => {
    // 保存原始的emit方法
    const originalEmit = socket.emit;
    
    // 重写emit方法
    socket.emit = function(event, ...args) {
      // 对参数进行日期格式化
      const formattedArgs = args.map(arg => {
        if (typeof arg === 'object' && arg !== null) {
          return deepFormatDates(arg);
        }
        return arg;
      });
      
      // 调用原始emit方法
      return originalEmit.apply(this, [event, ...formattedArgs]);
    };
    
    next();
  });

  // 连接事件
  io.on('connection', (socket) => {
    // 移除连接日志
    connectedClients.add(socket);

    // 断开连接
    socket.on('disconnect', () => {
      // 移除断开连接日志
      connectedClients.delete(socket);
    });

    // 客户端请求初始数据
    socket.on('get_all_data', async () => {
      try {
        // 初始化聚合数据容器
        const allFetchedData = {};
        const initialCounts = {};
        const allHistoricalBatches = [];

        // 获取所有流程的所有历史数据
        for (const processType of Object.keys(processMap)) { // Use processMap keys to ensure all types are covered
          const data = await processModel.queryAllByType(processType); // Fetch all data
          const formattedData = deepFormatDates(data); // Format dates
          
          allFetchedData[processType] = data; // Store raw data for aggregation
          processDataCache[processType] = data; // Update cache with all data

          // 发送每个流程的完整数据
          socket.emit('process_update', {
            processType,
            records: formattedData
          });

          // 收集批次信息和计算总数
          initialCounts[processType] = data.length;
          const processName = getProcessName(processType);
          const processBatchData = data.map(item => ({
            batchId: item.batch_id,
            processName,
            timestamp: item.timestamp, // Keep original timestamp for sorting
            employee: item.employee,
            company: item.company
          }));
          allHistoricalBatches.push(...processBatchData);
        }

        // 计算并发送初始总数（所有时间）
        processCountsCache = initialCounts;
        socket.emit('process_counts', processCountsCache);

        // 计算并发送初始最近批次（所有时间中最新的10条）
        const initialRecentBatches = allHistoricalBatches
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)) // Sort by actual date objects
          .slice(0, 10);
        
        const formattedBatches = deepFormatDates(initialRecentBatches); // Format dates for emission
        recentBatchesCache = initialRecentBatches; // Update cache with unformatted dates for comparison
        socket.emit('recent_batches', formattedBatches);

      } catch (error) {
        console.error('Error handling get_all_data:', error); // Log errors
      }
    });
  });

  // 启动数据轮询和广播
  startDataPolling(io);
};

// 轮询数据并广播更新
async function startDataPolling(io) {
  try {
    // 立即执行一次数据更新
    await updateAllData(io);

    // 设置轮询间隔 (每2秒)
    setInterval(async () => {
      await updateAllData(io);
    }, 2000);
  } catch (error) {
    // 移除错误日志打印
  }
}

// 更新所有数据
async function updateAllData(io) {
  try {
    // 不再需要 startTime 和 endTime
    // const today = moment().format('YYYY-MM-DD');
    // const startTime = `${today} 00:00:00`;
    // const endTime = `${today} 23:59:59`;

    const processTypes = Object.keys(processMap); // Use processMap keys
    const allHistoricalBatches = [];
    const counts = {};
    let processDataChanged = false;
    let countsChanged = false;
    let recentBatchesChanged = false;

    for (const processType of processTypes) {
      // 获取该流程的所有历史数据
      const data = await processModel.queryAllByType(processType); 
      const formattedData = deepFormatDates(data);
      const formattedCache = deepFormatDates(processDataCache[processType]);

      // 检查流程数据是否有变化
      if (JSON.stringify(formattedData) !== JSON.stringify(formattedCache)) {
        processDataCache[processType] = data; // Update cache with all data
        // 广播更新 - 发送所有历史数据
        io.emit('process_update', {
          processType,
          records: formattedData
        });
        processDataChanged = true; // Mark that some process data changed
      }

      // 更新总数（基于所有历史数据）
      counts[processType] = data.length;

      // 收集所有历史批次信息
      const processName = getProcessName(processType);
      const processBatchData = data.map(item => ({
        batchId: item.batch_id,
        processName,
        timestamp: item.timestamp, // Keep original for sorting
        employee: item.employee,
        company: item.company
      }));
      allHistoricalBatches.push(...processBatchData);
    }

    // 检查总数是否有变化
    if (JSON.stringify(counts) !== JSON.stringify(processCountsCache)) {
      processCountsCache = counts;
      io.emit('process_counts', counts);
      countsChanged = true;
    }

    // 获取所有历史记录中最新的10条
    const recentBatches = allHistoricalBatches
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)) // Sort by date object
      .slice(0, 10);
      
    const formattedBatches = deepFormatDates(recentBatches); // Format dates for emission

    // 检查最近批次是否有变化 (compare formatted with formatted cache)
    if (JSON.stringify(formattedBatches) !== JSON.stringify(deepFormatDates(recentBatchesCache))) {
      recentBatchesCache = recentBatches; // Update cache with unformatted dates
      io.emit('recent_batches', formattedBatches);
      recentBatchesChanged = true;
    }

    // Optional: Log if any update was sent
    // if (processDataChanged || countsChanged || recentBatchesChanged) {
    //   console.log('Data updated and broadcasted.');
    // }

  } catch (error) {
    console.error('Error in updateAllData polling:', error); // Log errors
  }
}

// 获取流程类型的中文名称
function getProcessName(processType) {
  const nameMap = {
    'storage': '入库',
    'film': '贴膜',
    'cutting': '切割',
    'inspection': '检验',
    'shipping': '出货'
  };
  return nameMap[processType] || processType;
}

// 当新记录添加时，通知所有客户端 (此函数现在主要用于触发更新，实际数据由updateAllData处理)
// 或者也可以选择在这里直接推送完整的更新数据，避免依赖轮询的延迟
exports.notifyProcessUpdate = async (processType) => {
  try {
    // 获取更新后的该流程所有历史数据
    const data = await processModel.queryAllByType(processType);
    
    // 更新缓存
    processDataCache[processType] = data;
    
    if (ioInstance) {
      // 深度格式化所有日期
      const formattedData = deepFormatDates(data);
      
      // 广播更新 - 发送所有历史数据
      ioInstance.emit('process_update', {
        processType,
        records: formattedData
      });

      // --- OPTION A (Rely on polling): Comment out below, updateAllData will handle counts/recent batches ---
      
      // --- OPTION B (Update counts/recent immediately): Uncomment and adapt logic from updateAllData ---
      // // Recalculate and emit counts and recent batches immediately after a process update
      // // This avoids waiting for the next polling interval but adds more load here.
      // const allProcessTypes = Object.keys(processMap);
      // const allBatchesImmediate = [];
      // const countsImmediate = {};
      // for (const pt of allProcessTypes) {
      //   const currentData = processDataCache[pt]; // Use cache for other types
      //   countsImmediate[pt] = currentData.length;
      //   const name = getProcessName(pt);
      //   allBatchesImmediate.push(...currentData.map(item => ({ /* ... map batch data ... */ })));
      // }
      // if (JSON.stringify(countsImmediate) !== JSON.stringify(processCountsCache)) {
      //    processCountsCache = countsImmediate;
      //    ioInstance.emit('process_counts', countsImmediate);
      // }
      // const recentBatchesImmediate = allBatchesImmediate.sort(/*...*/).slice(0, 10);
      // const formattedRecentImmediate = deepFormatDates(recentBatchesImmediate);
      // if (JSON.stringify(formattedRecentImmediate) !== JSON.stringify(deepFormatDates(recentBatchesCache))) {
      //    recentBatchesCache = recentBatchesImmediate;
      //    ioInstance.emit('recent_batches', formattedRecentImmediate);
      // }
      // --- END OPTION B ---

    }
  } catch (error) {
    console.error(`Error in notifyProcessUpdate for ${processType}:`, error); // Log errors
  }
};

// Include processMap definition if not already globally available in this scope
const processMap = {
  'storage': 'storage',
  'film': 'film',
  'cutting': 'cutting',
  'inspection': 'inspection',
  'shipping': 'shipping'
};

module.exports = exports; // Ensure exports are correct 
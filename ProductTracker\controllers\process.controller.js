const processModel = require('../models/process.model');
const ExcelExporter = require('../utils/excelExporter');
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const moment = require('moment');
// 导入Socket控制器
const socketController = require('./socket.controller');

// 流程类型到中文名称的映射
const processNameMap = {
  'storage': '入库',
  'film': '贴膜',
  'cutting': '切割',
  'inspection': '检验',
  'shipping': '出货'
};

class ProcessController {
  // 解析二维码内容，提取客户代码和批次ID
  parseQRCode(qrContent) {
    // 如果二维码内容为空，返回空结果
    if (!qrContent) {
      console.log('解析二维码: 内容为空');
      return { companyCode: '', batchId: '' };
    }

    console.log('开始解析二维码内容:', qrContent);

    try {
      // 尝试解析二维码内容
      // 格式示例: "XG001-2025.5.4|6/HE095262.1/25"
      // 分隔符"|"左边的是客户代码，右边的是批次ID
      if (qrContent.includes('|')) {
        const parts = qrContent.split('|', 2);
        if (parts.length === 2) {
          const result = {
            companyCode: parts[0].trim(),
            batchId: parts[1].trim()
          };
          console.log('二维码解析成功:', result);
          return result;
        }
      }

      // 如果无法解析，则整个内容作为批次ID，客户代码为空
      console.log('二维码格式不符合预期，将整个内容作为批次ID');
      return {
        companyCode: '',
        batchId: qrContent.trim()
      };
    } catch (error) {
      console.error('二维码解析出错:', error);
      // 发生错误时，返回安全的默认值
      return {
        companyCode: '',
        batchId: qrContent ? qrContent.trim() : ''
      };
    }
  }

  // 记录批次操作
  async recordProcess(req, res) {
    try {
      console.log('接收到记录批次操作请求:');
      console.log('请求头:', req.headers);
      console.log('请求体:', req.body);

      const { batchId, processType, employee, company, qrContent } = req.body;

      // 记录详细的请求参数
      console.log('解析请求参数:', {
        batchId: batchId || '未提供',
        processType: processType || '未提供',
        employee: employee || '未提供',
        company: company || '未提供',
        qrContent: qrContent || '未提供',
        qrContentLength: qrContent ? qrContent.length : 0
      });

      // 验证参数
      if (!processType) {
        console.log('验证失败: 操作类型不能为空');
        return res.status(400).json({
          success: false,
          message: '操作类型不能为空'
        });
      }

      if (!employee) {
        console.log('验证失败: 员工信息不能为空');
        return res.status(400).json({
          success: false,
          message: '员工信息不能为空'
        });
      }

      // 检查是否提供了二维码内容或批次ID
      if (!qrContent && !batchId) {
        console.log('验证失败: 二维码内容和批次ID不能同时为空');
        return res.status(400).json({
          success: false,
          message: '二维码内容和批次ID不能同时为空'
        });
      }

      // 解析二维码内容，提取客户代码和批次ID
      let parsedCompanyCode = '';
      let parsedBatchId = batchId; // 默认使用传入的批次ID

      if (qrContent) {
        try {
          console.log('开始解析二维码内容...');
          const parseResult = this.parseQRCode(qrContent);
          parsedCompanyCode = parseResult.companyCode;

          // 如果没有提供批次ID，则使用解析出的批次ID
          if (!batchId) {
            parsedBatchId = parseResult.batchId;
          }
          console.log('二维码解析完成');
        } catch (parseError) {
          console.error('二维码解析失败:', parseError);
          // 解析失败时，使用原始内容作为批次ID
          if (!batchId) {
            parsedBatchId = qrContent.trim();
          }
        }
      }

      // 使用解析出的客户代码，如果为空则使用传入的公司信息
      const finalCompanyCode = parsedCompanyCode || company || '';

      // 验证最终的批次ID
      if (!parsedBatchId) {
        console.log('验证失败: 无法获取有效的批次ID');
        return res.status(400).json({
          success: false,
          message: '无法获取有效的批次ID'
        });
      }

      console.log('处理二维码:', {
        原始二维码: qrContent,
        解析的客户代码: parsedCompanyCode,
        解析的批次ID: parsedBatchId,
        最终使用的客户代码: finalCompanyCode,
        最终使用的批次ID: parsedBatchId
      });

      console.log('调用模型记录批次操作...');
      const result = await processModel.recordProcess(parsedBatchId, processType, employee, finalCompanyCode);
      console.log('批次操作记录成功:', result);

      // 通知WebSocket客户端数据已更新
      await socketController.notifyProcessUpdate(processType);
      console.log('WebSocket通知已发送');

      return res.status(200).json(result);
    } catch (error) {
      console.error('记录批次操作失败:', error);
      console.error('错误堆栈:', error.stack);
      return res.status(400).json({
        success: false,
        message: `操作失败: ${error.message}`
      });
    }
  }

  // 根据时间范围查询数据
  async queryByTimeRange(req, res) {
    try {
      // 同时支持查询参数和请求体
      let processType, startTime, endTime;

      console.log('接收到查询请求:', {
        query: req.query,
        body: req.body
      });

      if (req.query.processType || req.query.startTime || req.query.endTime) {
        // 从URL查询参数获取
        processType = req.query.processType;
        startTime = req.query.startTime;
        endTime = req.query.endTime;
        console.log('从URL查询参数获取:', { processType, startTime, endTime });
      } else if (req.body && Object.keys(req.body).length > 0) {
        // 从请求体获取
        processType = req.body.processType;
        startTime = req.body.startTime;
        endTime = req.body.endTime;
        console.log('从请求体获取:', { processType, startTime, endTime });
      }

      // 验证参数
      if (!processType || !startTime || !endTime) {
        console.log('参数验证失败:', { processType, startTime, endTime });
        return res.status(400).json({
          success: false,
          message: '操作类型、开始时间和结束时间不能为空'
        });
      }

      console.log('开始查询数据:', { processType, startTime, endTime });
      const data = await processModel.queryByTimeRange(processType, startTime, endTime);
      console.log('查询结果数据条数:', data.length);

      // 使用moment格式化时间戳
      const formattedData = data.map(item => ({
        ...item,
        timestamp: moment(item.timestamp).format('YYYY-MM-DD HH:mm:ss')
      }));

      console.log('返回数据成功');
      return res.status(200).json({
        success: true,
        data: formattedData
      });
    } catch (error) {
      console.error('查询数据失败:', error);
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  // 导出Excel文件
  async exportExcel(req, res) {
    try {
      // 同时支持查询参数和请求体
      let processType, startTime, endTime;

      if (req.query.processType || req.query.startTime || req.query.endTime) {
        // 从URL查询参数获取
        processType = req.query.processType;
        startTime = req.query.startTime;
        endTime = req.query.endTime;
      } else if (req.body && Object.keys(req.body).length > 0) {
        // 从请求体获取
        processType = req.body.processType;
        startTime = req.body.startTime;
        endTime = req.body.endTime;
      }

      // 验证参数
      if (!processType || !startTime || !endTime) {
        return res.status(400).json({
          success: false,
          message: '操作类型、开始时间和结束时间不能为空'
        });
      }

      const data = await processModel.queryByTimeRange(processType, startTime, endTime);

      // 打印查询结果的第一条数据，检查是否包含员工和公司字段
      console.log('查询结果数据条数:', data.length);
      if (data.length > 0) {
        console.log('第一条数据:', JSON.stringify(data[0]));
      }

      // 确保导出目录存在
      const exportDir = path.join(__dirname, '../exports');
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      // 生成Excel文件
      const exporter = new ExcelExporter();
      exporter.initSheet(`${processNameMap[processType]}数据`);
      console.log('开始导出Excel数据...');
      exporter.addData(data);

      // 使用英文文件名以避免编码问题
      const timestamp = new Date().getTime();
      const filename = `${processType}_data_${timestamp}.xlsx`;
      const filePath = path.join(exportDir, filename);

      await exporter.generateExcel(filePath);

      // 设置响应头并发送文件
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);

      // 使用fs流直接发送文件，而不是使用res.sendFile
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      // 文件发送完成后不删除临时文件，以便检查
      fileStream.on('end', () => {
        console.log('文件发送完成，保留临时文件以便检查:', filePath);
        // fs.unlinkSync(filePath);
      });

      fileStream.on('error', (err) => {
        console.error('文件流错误:', err);
        if (!res.headersSent) {
          return res.status(500).json({
            success: false,
            message: '文件下载失败'
          });
        }
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  // 获取批次所有流程数据
  async getBatchData(req, res) {
    try {
      const { batchId } = req.params;

      if (!batchId) {
        return res.status(400).json({
          success: false,
          message: '批次ID不能为空'
        });
      }

      const data = await processModel.getAllProcessData(batchId);

      // 格式化各个流程的时间戳并移除created_at字段
      Object.keys(data).forEach(processType => {
        if (data[processType]) {
          if (data[processType].timestamp) {
            data[processType].timestamp = moment(data[processType].timestamp).format('YYYY-MM-DD HH:mm:ss');
          }
          // 移除created_at字段
          if (data[processType].created_at) {
            delete data[processType].created_at;
          }
        }
      });

      return res.status(200).json({
        success: true,
        data
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  // 根据批次ID和流程类型查询数据
  async queryByBatchAndProcess(req, res) {
    try {
      // 从请求体获取参数
      const { batchId, processType } = req.body;

      // 验证参数
      if (!batchId || !processType) {
        return res.status(400).json({
          success: false,
          message: '批次ID和操作类型不能为空'
        });
      }

      const data = await processModel.queryByBatchAndProcess(batchId, processType);

      // 使用moment格式化时间戳
      const formattedData = data.map(item => ({
        ...item,
        timestamp: moment(item.timestamp).format('YYYY-MM-DD HH:mm:ss')
      }));

      return res.status(200).json({
        success: true,
        data: formattedData
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 导出所有流程数据为Excel
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async exportAllProcessData(req, res) {
    try {
      const { startTime, endTime } = req.query;

      if (!startTime || !endTime) {
        return res.status(400).json({
          success: false,
          message: '开始时间和结束时间不能为空'
        });
      }

      // 创建工作簿
      const workbook = new ExcelJS.Workbook();

      // 为每个流程创建工作表
      for (const processType of ['storage', 'film', 'cutting', 'inspection', 'shipping']) {
        const worksheet = workbook.addWorksheet(processType);

        // 设置表头
        worksheet.columns = [
          { header: 'ID', key: 'id', width: 10 },
          { header: '批次ID', key: 'batch_id', width: 20 },
          { header: '时间', key: 'timestamp', width: 20 },
          { header: '员工', key: 'employee', width: 15 },
          { header: '公司', key: 'company', width: 25 }
        ];

        // 查询数据
        const records = await processModel.queryByTimeRange(processType, startTime, endTime);

        // 添加数据
        records.forEach(record => {
          // 使用moment格式化时间戳
          const formattedTimestamp = moment(record.timestamp).format('YYYY-MM-DD HH:mm:ss');

          worksheet.addRow({
            id: record.id,
            batch_id: record.batch_id,
            timestamp: formattedTimestamp,
            employee: record.employee || '未知',
            company: record.company || '未知'
          });
        });
      }

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=all_process_data_${moment().format('YYYYMMDD_HHmmss')}.xlsx`
      );

      // 发送文件
      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      console.error('导出所有流程数据失败:', error);
      res.status(500).json({
        success: false,
        message: '导出所有流程数据失败',
        error: error.message
      });
    }
  }

  /**
   * 更新所有记录的员工和公司信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateEmployeeAndCompany(req, res) {
    try {
      const result = await processModel.updateAllEmployeeAndCompany();

      // 更新完成后，通知所有客户端更新数据
      for (const processType of ['storage', 'film', 'cutting', 'inspection', 'shipping']) {
        await socketController.notifyProcessUpdate(processType);
      }

      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: '更新员工和公司信息失败',
        error: error.message
      });
    }
  }

  /**
   * 更新批次信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateBatchInfo(req, res) {
    try {
      const { batchId } = req.params;
      const { newBatchId, employee, company, processTypeForEmployee } = req.body;

      console.log('接收到更新批次信息请求:', {
        batchId,
        newBatchId,
        employee,
        company,
        processTypeForEmployee
      });

      // 验证参数
      if (!batchId) {
        return res.status(400).json({
          success: false,
          message: '批次ID不能为空'
        });
      }

      // 至少需要一个要更新的字段
      if (!newBatchId && !employee && !company) {
        return res.status(400).json({
          success: false,
          message: '至少需要提供一个要更新的字段'
        });
      }

      // 如果提供了员工信息但没有指定流程类型，返回错误
      if (employee && !processTypeForEmployee) {
        return res.status(400).json({
          success: false,
          message: '更新员工信息时需要指定流程类型'
        });
      }

      // 调用模型方法更新批次信息
      const result = await processModel.updateBatchInfo(batchId, {
        newBatchId,
        employee,
        company,
        processTypeForEmployee
      });

      // 更新完成后，通知所有客户端更新数据
      for (const processType of ['storage', 'film', 'cutting', 'inspection', 'shipping']) {
        await socketController.notifyProcessUpdate(processType);
      }

      return res.status(200).json({
        success: true,
        message: `批次 ${batchId} 的信息已更新`,
        data: result
      });
    } catch (error) {
      console.error('更新批次信息失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新批次信息失败',
        error: error.message
      });
    }
  }

  /**
   * 删除批次信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteBatchInfo(req, res) {
    try {
      const { batchId } = req.params;

      console.log('接收到删除批次信息请求:', {
        batchId
      });

      // 验证参数
      if (!batchId) {
        return res.status(400).json({
          success: false,
          message: '批次ID不能为空'
        });
      }

      // 调用模型方法删除批次信息
      const result = await processModel.deleteBatchInfo(batchId);

      // 删除完成后，通知所有客户端更新数据
      for (const processType of ['storage', 'film', 'cutting', 'inspection', 'shipping']) {
        await socketController.notifyProcessUpdate(processType);
      }

      return res.status(200).json({
        success: true,
        message: `批次 ${batchId} 的记录已完全删除`,
        data: result
      });
    } catch (error) {
      console.error('删除批次信息失败:', error);
      console.error('错误堆栈:', error.stack);
      console.error('批次ID:', batchId);
      return res.status(500).json({
        success: false,
        message: '删除批次信息失败',
        error: error.message
      });
    }
  }

  /**
   * 删除客户代码相关的所有数据
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteCompanyData(req, res) {
    try {
      const { company } = req.params;

      console.log('接收到删除客户代码数据请求:', {
        company
      });

      // 验证参数
      if (!company) {
        return res.status(400).json({
          success: false,
          message: '客户代码不能为空'
        });
      }

      // 调用模型方法删除客户代码相关数据
      const result = await processModel.deleteCompanyData(company);

      // 删除完成后，通知所有客户端更新数据
      for (const processType of ['storage', 'film', 'cutting', 'inspection', 'shipping']) {
        await socketController.notifyProcessUpdate(processType);
      }

      return res.status(200).json({
        success: true,
        message: `客户代码 ${company} 的所有数据已删除`,
        data: result
      });
    } catch (error) {
      console.error('删除客户代码数据失败:', error);
      console.error('错误堆栈:', error.stack);
      console.error('客户代码:', req.params.company);
      return res.status(500).json({
        success: false,
        message: '删除客户代码数据失败',
        error: error.message
      });
    }
  }
}

module.exports = new ProcessController();
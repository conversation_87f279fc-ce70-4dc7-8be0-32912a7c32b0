import React, { useState, useEffect } from 'react';
import { Card, Input, Table, message } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSocketStore } from '../utils/socket';

const ProcessDetail = ({ processType, processName }) => {
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(true);
  
  const { processData } = useSocketStore();
  
  // 获取当前流程数据
  const records = processData[processType] || [];
  
  // 创建搜索条件的计算属性
  const filteredRecords = records.filter(record => {
    if (!searchText) {
      return true;
    }
    const search = searchText.toLowerCase();
    return (
      record.batch_id.toString().toLowerCase().includes(search) ||
      (record.operator && record.operator.toLowerCase().includes(search)) ||
      (record.timestamp && record.timestamp.toLowerCase().includes(search))
    );
  });
  
  // 设置流程类型和名称
  const getProcessName = (type) => {
    switch (type) {
      case 'storage':
        return '入库';
      case 'film':
        return '覆膜';
      case 'cutting':
        return '分切';
      case 'inspection':
        return '检验';
      case 'shipping':
        return '出货';
      default:
        return '未知流程';
    }
  };
  
  // 基础列定义
  const baseColumns = [
    {
      title: '批次ID',
      dataIndex: 'batch_id',
      key: 'batch_id',
      width: 120,
    },
    {
      title: '操作员',
      dataIndex: 'operator',
      key: 'operator',
      width: 120,
    },
    {
      title: '操作时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
    },
  ];
  
  // 根据流程类型添加额外列
  const getColumns = () => {
    let additionalColumns = [];
    
    switch (processType) {
      case 'film':
        additionalColumns = [
          {
            title: '覆膜类型',
            dataIndex: 'film_type',
            key: 'film_type',
          },
          {
            title: '机器ID',
            dataIndex: 'machine_id',
            key: 'machine_id',
            width: 100,
          },
          {
            title: '处理时间(分钟)',
            dataIndex: 'duration',
            key: 'duration',
            width: 140,
          },
        ];
        break;
      case 'cutting':
        additionalColumns = [
          {
            title: '宽度(mm)',
            dataIndex: 'width',
            key: 'width',
            width: 100,
          },
          {
            title: '长度(m)',
            dataIndex: 'length',
            key: 'length',
            width: 100,
          },
          {
            title: '机器ID',
            dataIndex: 'machine_id',
            key: 'machine_id',
            width: 100,
          },
        ];
        break;
      case 'inspection':
        additionalColumns = [
          {
            title: '质量等级',
            dataIndex: 'quality_grade',
            key: 'quality_grade',
            width: 100,
          },
          {
            title: '缺陷数量',
            dataIndex: 'defect_count',
            key: 'defect_count',
            width: 100,
          },
          {
            title: '是否通过',
            dataIndex: 'pass_status',
            key: 'pass_status',
            width: 100,
          },
        ];
        break;
      case 'shipping':
        additionalColumns = [
          {
            title: '目的地',
            dataIndex: 'destination',
            key: 'destination',
          },
          {
            title: '承运商',
            dataIndex: 'carrier',
            key: 'carrier',
          },
          {
            title: '跟踪号',
            dataIndex: 'tracking_number',
            key: 'tracking_number',
          },
        ];
        break;
      default:
        break;
    }
    
    return [...baseColumns, ...additionalColumns];
  };
  
  // 组件挂载时
  useEffect(() => {
    if (!processType) {
      message.error('未指定流程类型');
    } else {
      setLoading(false);
    }
  }, [processType]);
  
  return (
    <Card title={`${processName || getProcessName(processType)}流程详情`}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
        <div style={{ width: '300px' }}>
          <Input
            placeholder="搜索批次ID、操作员或时间..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </div>
      </div>
      
      <Table 
        dataSource={filteredRecords} 
        columns={getColumns()} 
        rowKey={(record) => `${record.batch_id}_${record.timestamp}`}
        loading={loading}
        bordered
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />
    </Card>
  );
};

export default ProcessDetail;

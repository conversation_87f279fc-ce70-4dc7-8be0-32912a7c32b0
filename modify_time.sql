UPDATE storage
SET `timestamp` = CONCAT('2025-04-05', <PERSON><PERSON><PERSON><PERSON>(`timestamp`, 11)),
    created_at = CONCAT('2025-04-05', SUBSTR(created_at, 11));

UPDATE film
SET `timestamp` = CONCAT('2025-04-05', S<PERSON><PERSON>TR(`timestamp`, 11)),
    created_at = CONCAT('2025-04-05', S<PERSON><PERSON>TR(created_at, 11));

UPDATE cutting
SET `timestamp` = CONCAT('2025-04-05', SUBSTR(`timestamp`, 11)),
    created_at = CONCAT('2025-04-05', SUBSTR(created_at, 11));

UPDATE inspection
SET `timestamp` = CONCAT('2025-04-05', <PERSON><PERSON><PERSON><PERSON>(`timestamp`, 11)),
    created_at = CONCAT('2025-04-05', S<PERSON><PERSON>TR(created_at, 11));


UPDATE shipping
SET `timestamp` = CONCAT('2025-04-05', <PERSON><PERSON><PERSON>TR(`timestamp`, 11)),
    created_at = CONCAT('2025-04-05', S<PERSON><PERSON><PERSON>(created_at, 11));
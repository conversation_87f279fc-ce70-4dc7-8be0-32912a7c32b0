import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Row,
  Col,
  Descriptions,
  Alert,
  message,
  Select
} from 'antd';
import { SearchOutlined, EditOutlined } from '@ant-design/icons';
import { getBatchData, updateBatchInfo } from '../utils/api';
import { PROCESS_TYPES } from '../utils/constants';

const BatchEditor = () => {
  const [form] = Form.useForm();
  const [searching, setSearching] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [notFound, setNotFound] = useState(false);
  const [selectedBatch, setSelectedBatch] = useState(null);

  // 计算是否有修改
  const hasChanges = () => {
    if (!selectedBatch) return false;

    const newBatchId = form.getFieldValue('newBatchId');
    const newEmployee = form.getFieldValue('newEmployee');
    const newCompany = form.getFieldValue('newCompany');

    return (
      (newBatchId && newBatchId !== selectedBatch.batch_id) ||
      (newEmployee && newEmployee !== selectedBatch.employee) ||
      (newCompany && newCompany !== selectedBatch.company)
    );
  };

  // 获取流程类型的中文名称
  const getProcessLabel = (processType) => {
    const process = PROCESS_TYPES.find(p => p.value === processType);
    return process ? process.label : (processType || '未知');
  };

  // 查询批次信息
  const searchBatch = async () => {
    const originalBatchId = form.getFieldValue('originalBatchId');

    if (!originalBatchId) {
      message.warning('请输入批次ID');
      return;
    }

    setSearching(true);
    setNotFound(false);
    setSelectedBatch(null);

    try {
      const response = await getBatchData(originalBatchId);

      if (response.data && response.data.success && response.data.data) {
        setSelectedBatch(response.data.data);
        // 预填充表单
        form.setFieldsValue({
          newBatchId: '',
          newEmployee: '',
          newCompany: ''
        });
        message.success('批次信息获取成功');
      } else {
        setNotFound(true);
        message.warning('未找到该批次信息');
      }
    } catch (error) {
      console.error('获取批次信息失败:', error);
      message.error('获取批次信息失败: ' + (error.response?.data?.message || error.message));
      setNotFound(true);
    } finally {
      setSearching(false);
    }
  };

  // 提交修改
  const submitUpdate = async () => {
    if (!selectedBatch) {
      message.warning('请先查询批次信息');
      return;
    }

    if (!hasChanges()) {
      message.warning('未检测到任何修改');
      return;
    }

    // 构建要更新的数据
    const updateData = {};
    const values = form.getFieldsValue();

    if (values.newBatchId) {
      updateData.newBatchId = values.newBatchId;
    }

    if (values.newEmployee) {
      updateData.employee = values.newEmployee;

      // 如果提供了员工信息，必须指定流程类型
      if (!values.processTypeForEmployee) {
        message.warning('更新员工信息时需要选择流程步骤');
        return;
      }

      // 将流程类型添加到更新数据中
      updateData.processTypeForEmployee = values.processTypeForEmployee;
    }

    if (values.newCompany) {
      updateData.company = values.newCompany;
    }

    setUpdating(true);

    try {
      const response = await updateBatchInfo(values.originalBatchId, updateData);

      if (response.data && response.data.success) {
        message.success('批次信息修改成功');
        resetForm();
      } else {
        message.error('修改失败: ' + (response.data?.message || '未知错误'));
      }
    } catch (error) {
      console.error('修改批次信息失败:', error);
      message.error('修改失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setUpdating(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    form.resetFields();
    setSelectedBatch(null);
    setNotFound(false);
  };

  return (
    <Card title="批次信息修改">
      <Form form={form} layout="vertical">
        <Form.Item
          label="批次ID"
          name="originalBatchId"
          rules={[{ required: true, message: '请输入需要修改的批次ID' }]}
        >
          <Row gutter={10}>
            <Col span={18}>
              <Input
                placeholder="输入需要修改的批次ID"
                disabled={!!selectedBatch}
                allowClear
              />
            </Col>
            <Col span={6}>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={searchBatch}
                disabled={!!selectedBatch}
                loading={searching}
                block
              >
                查询
              </Button>
            </Col>
          </Row>
        </Form.Item>

        {selectedBatch && (
          <>
            <div style={{ marginTop: '30px', marginBottom: '30px' }}>
              <h3 style={{
                marginBottom: '15px',
                paddingBottom: '10px',
                borderBottom: '1px solid #ebeef5',
                color: '#303133'
              }}>
                当前批次信息
              </h3>
              <Descriptions bordered column={1}>
                <Descriptions.Item label="批次ID">{selectedBatch.batch_id}</Descriptions.Item>
                <Descriptions.Item label="员工">{selectedBatch.employee}</Descriptions.Item>
                <Descriptions.Item label="客户代码">{selectedBatch.company}</Descriptions.Item>
                <Descriptions.Item label="最后更新">{selectedBatch.timestamp}</Descriptions.Item>
                <Descriptions.Item label="流程">{getProcessLabel(selectedBatch.process_type)}</Descriptions.Item>
              </Descriptions>
            </div>

            <div style={{ marginTop: '30px', marginBottom: '30px' }}>
              <h3 style={{
                marginBottom: '15px',
                paddingBottom: '10px',
                borderBottom: '1px solid #ebeef5',
                color: '#303133'
              }}>
                修改信息
              </h3>

              <Form.Item
                label="新批次ID"
                name="newBatchId"
              >
                <Input placeholder="输入新的批次ID (留空表示不修改)" allowClear />
              </Form.Item>

              <Form.Item label="新员工" name="newEmployee">
                <Input placeholder="输入新的员工姓名 (留空表示不修改)" allowClear />
              </Form.Item>

              <Form.Item
                label="员工所属流程"
                name="processTypeForEmployee"
                help="选择要修改员工信息的流程步骤，只有该步骤的员工信息会被修改"
              >
                <Select placeholder="选择要修改员工信息的流程步骤" allowClear>
                  {selectedBatch.storage && <Select.Option value="storage">入库</Select.Option>}
                  {selectedBatch.film && <Select.Option value="film">贴膜</Select.Option>}
                  {selectedBatch.cutting && <Select.Option value="cutting">切割</Select.Option>}
                  {selectedBatch.inspection && <Select.Option value="inspection">检验</Select.Option>}
                  {selectedBatch.shipping && <Select.Option value="shipping">出货</Select.Option>}
                </Select>
              </Form.Item>

              <Form.Item label="新客户代码" name="newCompany">
                <Input placeholder="输入新的客户代码 (留空表示不修改)" allowClear />
              </Form.Item>

              <Alert
                message="修改说明"
                description={
                  <div>
                    <p><strong>批次ID和客户代码</strong>的修改将应用于批次已完成的所有流程步骤中。</p>
                    <p style={{ color: '#1890ff', marginTop: '10px' }}><strong>员工信息</strong>的修改只会应用于您选择的特定流程步骤，其他步骤的员工信息不受影响。</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: '20px' }}
              />

              <Form.Item>
                <Row gutter={10} justify="center">
                  <Col xs={24} sm={12}>
                    <Button
                      type="primary"
                      icon={<EditOutlined />}
                      onClick={submitUpdate}
                      loading={updating}
                      disabled={!hasChanges()}
                      block
                    >
                      提交修改
                    </Button>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Button
                      type="default"
                      onClick={resetForm}
                      block
                    >
                      取消
                    </Button>
                  </Col>
                </Row>
              </Form.Item>
            </div>
          </>
        )}

        {notFound && (
          <Alert
            message="未找到该批次"
            description="请检查批次ID是否正确"
            type="warning"
            showIcon
            style={{ marginTop: '20px' }}
          />
        )}

        {!selectedBatch && !notFound && (
          <Alert
            message="使用说明"
            description="输入要修改的批次ID并点击查询，然后可以修改批次信息"
            type="info"
            showIcon
            style={{ marginTop: '20px' }}
          />
        )}
      </Form>
    </Card>
  );
};

export default BatchEditor;

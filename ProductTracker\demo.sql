-- 创建数据库
CREATE DATABASE product_tracker;

-- 使用数据库
USE product_tracker;

-- 创建入库表
CREATE TABLE IF NOT EXISTS storage (
  id INT AUTO_INCREMENT PRIMARY KEY,
  batch_id VARCHAR(20) NOT NULL UNIQUE,
  timestamp DATETIME NOT NULL,
  employee VARCHAR(50) NOT NULL,
  company VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建贴膜表
CREATE TABLE IF NOT EXISTS film (
  id INT AUTO_INCREMENT PRIMARY KEY,
  batch_id VARCHAR(20) NOT NULL UNIQUE,
  timestamp DATETIME NOT NULL,
  employee VARCHAR(50) NOT NULL,
  company VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建切割表
CREATE TABLE IF NOT EXISTS cutting (
  id INT AUTO_INCREMENT PRIMARY KEY,
  batch_id VARCHAR(20) NOT NULL UNIQUE,
  timestamp DATETIME NOT NULL,
  employee VARCHAR(50) NOT NULL,
  company VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建检验表
CREATE TABLE IF NOT EXISTS inspection (
  id INT AUTO_INCREMENT PRIMARY KEY,
  batch_id VARCHAR(20) NOT NULL UNIQUE,
  timestamp DATETIME NOT NULL,
  employee VARCHAR(50) NOT NULL,
  company VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建出货表
CREATE TABLE IF NOT EXISTS shipping (
  id INT AUTO_INCREMENT PRIMARY KEY,
  batch_id VARCHAR(20) NOT NULL UNIQUE,
  timestamp DATETIME NOT NULL,
  employee VARCHAR(50) NOT NULL,
  company VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
安装 PM2:
bash
复制
编辑
npm install -g pm2
启动两个服务:
后端服务
bash
复制
编辑
cd ~/Inbound-and-outbound-management-system/ProductTracker
pm2 start app.js --name "product-tracker-backend"
前端服务
bash
复制
编辑
cd ~/Inbound-and-outbound-management-system
pm2 start "npm run dev" --name "product-tracker-frontend"
设置开机自启:
bash
复制
编辑
pm2 startup
pm2 save
查看运行状态:
bash
复制
编辑
pm2 status
查看日志:
bash
复制
编辑
pm2 logs




ALTER TABLE storage MODIFY COLUMN batch_id VARCHAR(50) NOT NULL UNIQUE;
ALTER TABLE film MODIFY COLUMN batch_id VARCHAR(50) NOT NULL UNIQUE;
ALTER TABLE cutting MODIFY COLUMN batch_id VARCHAR(50) NOT NULL UNIQUE;
ALTER TABLE inspection MODIFY COLUMN batch_id VARCHAR(50) NOT NULL UNIQUE;
ALTER TABLE shipping MODIFY COLUMN batch_id VARCHAR(50) NOT NULL UNIQUE;



DESCRIBE storage;
DESCRIBE film;
DESCRIBE cutting;
DESCRIBE inspection;
DESCRIBE shipping;
import axios from 'axios'

// 使用相对路径，通过Vite代理转发
const API_BASE_URL = '/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: false // 禁用凭证，解决CORS问题
})

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', {
      url: config.url,
      method: config.method,
      params: config.params,
      data: config.data
    });
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
api.interceptors.response.use(
  response => {
    console.log('响应成功:', {
      status: response.status,
      data: response.data
    });
    return response;
  },
  error => {
    console.error('响应错误:', error);
    return Promise.reject(error);
  }
);

export const getProcessData = (processType, startTime, endTime, filters = {}) => {
  // 构建查询参数
  const params = {};

  // 添加流程类型（除非是"all"）
  if (processType && processType !== 'all') {
    params.processType = processType;
  }

  // 添加时间范围（如果存在）
  if (startTime) {
    params.startTime = startTime;
  }
  if (endTime) {
    params.endTime = endTime;
  }

  // 添加筛选条件
  if (filters.batch_id) {
    params.batch_id = filters.batch_id;
  }
  if (filters.employee) {
    params.employee = filters.employee;
  }
  if (filters.company) {
    params.company = filters.company;
  }

  console.log('API请求参数:', params);
  console.log('API请求URL:', `${API_BASE_URL}/process`);

  return api.get('/process', { params })
    .then(response => {
      console.log('API请求成功:', response.data);
      return response;
    })
    .catch(error => {
      console.error('API请求失败:', error);
      throw error;
    });
}

export const getBatchData = (batchId) => {
  // 对批次ID进行URL编码，确保特殊字符（如斜杠）能够正确传递
  const encodedBatchId = encodeURIComponent(batchId);
  return api.get(`/process/${encodedBatchId}`)
}

export const exportProcessData = (processType, startTime, endTime, filters = {}) => {
  // 构建基本URL和参数
  const params = new URLSearchParams();

  // 添加流程类型
  params.append('processType', processType);

  // 添加时间范围（如果存在）
  if (startTime) {
    params.append('startTime', startTime);
  }
  if (endTime) {
    params.append('endTime', endTime);
  }

  // 添加额外筛选条件
  if (filters.batch_id) {
    params.append('batch_id', filters.batch_id);
  }
  if (filters.employee) {
    params.append('employee', filters.employee);
  }
  if (filters.company) {
    params.append('company', filters.company);
  }

  // 指定要导出的字段（确保包含员工和公司字段）
  params.append('fields', 'batch_id,timestamp,employee,company');
  params.append('format', 'excel');

  // 构建最终URL - 使用相对路径
  const url = `/api/process/export?${params.toString()}`;

  console.log('导出URL:', url);

  // 使用fetch下载文件，而不是window.open
  return fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`导出失败: ${response.status} ${response.statusText}`);
      }
      return response.blob();
    })
    .then(blob => {
      // 创建URL并触发下载
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${processType}_data_${new Date().getTime()}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      console.log('导出成功');
    })
    .catch(error => {
      console.error('导出错误:', error);
      throw error;
    });
}

export const exportAllProcessData = (startTime, endTime, filters = {}) => {
  // 构建基本URL和参数
  const params = new URLSearchParams();

  // 添加时间范围（如果存在）
  if (startTime) {
    params.append('startTime', startTime);
  }
  if (endTime) {
    params.append('endTime', endTime);
  }

  // 添加额外筛选条件
  if (filters.batch_id) {
    params.append('batch_id', filters.batch_id);
  }
  if (filters.employee) {
    params.append('employee', filters.employee);
  }
  if (filters.company) {
    params.append('company', filters.company);
  }

  // 指定要导出的字段（确保包含员工和公司字段）
  params.append('fields', 'batch_id,timestamp,employee,company');
  params.append('format', 'excel');

  // 构建最终URL - 使用相对路径
  const url = `/api/process/export-all?${params.toString()}`;

  console.log('导出所有数据URL:', url);

  // 使用fetch下载文件，而不是window.open
  return fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`导出失败: ${response.status} ${response.statusText}`);
      }
      return response.blob();
    })
    .then(blob => {
      // 创建URL并触发下载
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `all_process_data_${new Date().getTime()}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      console.log('导出所有数据成功');
    })
    .catch(error => {
      console.error('导出所有数据错误:', error);
      throw error;
    });
}

// 更新批次信息
export const updateBatchInfo = (batchId, updateData) => {
  // 对批次ID进行URL编码，确保特殊字符能够正确传递
  const encodedBatchId = encodeURIComponent(batchId);
  return api.post(`/process/update/${encodedBatchId}`, updateData);
}

// 删除批次信息
export const deleteBatchInfo = (batchId) => {
  // 对批次ID进行URL编码，确保特殊字符能够正确传递
  const encodedBatchId = encodeURIComponent(batchId);
  return api.delete(`/process/delete/${encodedBatchId}`);
}

// 删除客户代码相关的所有数据
export const deleteCompanyData = (company) => {
  // 对客户代码进行URL编码，确保特殊字符能够正确传递
  const encodedCompany = encodeURIComponent(company);
  return api.delete(`/process/delete-company/${encodedCompany}`);
}

export default api
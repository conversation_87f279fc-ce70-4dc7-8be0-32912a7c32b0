import React, { useState, useEffect } from 'react';
import { PageContainer, ProLayout } from '@ant-design/pro-components';
import { Tag } from 'antd';
import {
  DashboardOutlined,
  AppstoreOutlined,
  InboxOutlined,
  ScissorOutlined,
  CheckCircleOutlined,
  CarOutlined,
  FilterOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons';
import Dashboard from './components/Dashboard';
import ProcessTable from './components/ProcessTable';
import ExportPanel from './components/ExportPanel';
import { useSocketStore } from './utils/socket';
import socketService from './utils/socket';

const App = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('1');

  const {
    connectionStatus,
    lastUpdateTime
  } = useSocketStore();

  // 获取连接状态标签类型
  const getConnectionStatusType = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'success';
      case 'disconnected':
        return 'error';
      case 'error':
        return 'warning';
      default:
        return 'default';
    }
  };

  // 获取连接状态文本
  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return '数据实时连接中';
      case 'disconnected':
        return '未连接实时数据';
      case 'error':
        return '连接错误';
      default:
        return '连接中...';
    }
  };

  // 组件挂载时初始化WebSocket
  useEffect(() => {
    // 设置WebSocket事件监听
    socketService.setupSocketListeners();
    // 连接WebSocket
    socketService.connectSocket();
    // 连接成功后请求初始数据
    setTimeout(() => {
      socketService.requestInitialData();
    }, 1000);

    // 组件卸载时断开WebSocket连接
    return () => {
      socketService.disconnectSocket();
    };
  }, []);

  // 渲染内容
  const renderContent = () => {
    switch (selectedKey) {
      case '1':
        return <Dashboard showCompletionRates={true} />;
      case '2-1':
        return <Dashboard showCompletionRates={false} />;
      case '2-2':
        return <ProcessTable processType="storage" processName="入库" />;
      case '2-3':
        return <ProcessTable processType="film" processName="贴膜" />;
      case '2-4':
        return <ProcessTable processType="cutting" processName="切割" />;
      case '2-5':
        return <ProcessTable processType="inspection" processName="检验" />;
      case '2-6':
        return <ProcessTable processType="shipping" processName="出货" />;
      case '3':
        return <ExportPanel />;
      default:
        return <Dashboard showCompletionRates={true} />;
    }
  };

  return (
    <div
      style={{
        height: '100vh',
      }}
    >
      <ProLayout
        title="生产流程追踪系统"
        logo="/XG.ico"
        collapsed={collapsed}
        onCollapse={setCollapsed}
        headerContentRender={() => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              onClick={() => setCollapsed(!collapsed)}
              style={{ cursor: 'pointer', fontSize: '16px', marginRight: '12px' }}
            >
              {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            </div>
          </div>
        )}
        rightContentRender={() => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* 移除了连接状态标签，将在左下角重新添加 */}
          </div>
        )}
        menuItemRender={(item, dom) => (
          <div onClick={() => setSelectedKey(item.key)}>{dom}</div>
        )}
        route={{
          routes: [
            {
              path: '/dashboard',
              name: '仪表盘',
              key: '1',
              icon: <DashboardOutlined />
            },
            {
              path: '/process',
              name: '流程追踪',
              icon: <AppstoreOutlined />,
              routes: [
                {
                  path: '/process/overview',
                  name: '总览',
                  key: '2-1',
                  icon: <AppstoreOutlined />
                },
                {
                  path: '/process/storage',
                  name: '入库',
                  key: '2-2',
                  icon: <InboxOutlined />
                },
                {
                  path: '/process/film',
                  name: '贴膜',
                  key: '2-3',
                  icon: <AppstoreOutlined />
                },
                {
                  path: '/process/cutting',
                  name: '切割',
                  key: '2-4',
                  icon: <ScissorOutlined />
                },
                {
                  path: '/process/inspection',
                  name: '检验',
                  key: '2-5',
                  icon: <CheckCircleOutlined />
                },
                {
                  path: '/process/shipping',
                  name: '出货',
                  key: '2-6',
                  icon: <CarOutlined />
                }
              ]
            },
            {
              path: '/export',
              name: '数据筛选与导出',
              key: '3',
              icon: <FilterOutlined />
            }
          ]
        }}
        menuProps={{
          selectedKeys: [selectedKey]
        }}
      >
        {/* 固定在左下角的连接状态标签 */}
        <div
          style={{
            position: 'fixed',
            left: '20px',
            bottom: '20px',
            zIndex: 1000,
            transition: 'all 0.3s'
          }}
        >
          <Tag color={getConnectionStatusType()}>{getConnectionStatusText()}</Tag>
        </div>
        <PageContainer
          header={{
            title: '',
            ghost: true,
            breadcrumb: {}
          }}
          style={{
            height: 'calc(100vh - 48px)',
            overflow: 'auto' // 允许内容溢出时显示滚动条
          }}
          footer={[]}
          footerToolBarProps={{
            style: {
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '6px 0',
              height: '29px' // 增加到1.2倍，约24px * 1.2 = 29px
            },
            extra: <div style={{
              width: '100%',
              textAlign: 'center',
              color: '#909399',
              fontSize: '12px',
              lineHeight: '17px' // 增加行高，使其在更高的元素中垂直居中
            }}>
              © 2025 江苏芯格电子科技有限公司
            </div>
          }}
        >
          {renderContent()}
        </PageContainer>
      </ProLayout>
    </div>
  );
};

export default App;

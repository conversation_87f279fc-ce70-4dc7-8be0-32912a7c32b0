const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// 读取Excel文件
const filePath = path.join(__dirname, '../test.xlsx');
const workbook = XLSX.readFile(filePath);

// 获取第一个工作表
const sheetName = workbook.SheetNames[0];
const worksheet = workbook.Sheets[sheetName];

// 将工作表转换为JSON
const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

// 打印工作表信息
console.log('工作表名称:', sheetName);
console.log('工作表行数:', data.length);
console.log('工作表列数:', data[0] ? data[0].length : 0);

// 打印表头
if (data.length > 0) {
  console.log('表头:', data[0]);
}

// 打印前5行数据
console.log('前5行数据:');
for (let i = 1; i < Math.min(6, data.length); i++) {
  console.log(`第${i}行:`, data[i]);
}

const { pool } = require('../config/db');
const moment = require('moment');

// 操作类型与对应的表名映射
const processMap = {
  'storage': 'storage',      // 入库
  'film': 'film',            // 贴膜
  'cutting': 'cutting',      // 切割
  'inspection': 'inspection', // 检验
  'shipping': 'shipping'     // 出货
};

// 操作顺序
const processSequence = ['storage', 'film', 'cutting', 'inspection', 'shipping'];

// 格式化日期时间，直接在数据层就进行格式化
function formatDateTime(timestamp) {
  return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
}

class ProcessModel {
  // 记录批次操作
  async recordProcess(batchId, processType, employee, company) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 检查操作类型是否有效
      if (!processMap[processType]) {
        throw new Error('无效的操作类型');
      }

      // 检查是否已经在此流程中记录过
      const [existingRecords] = await connection.execute(
        `SELECT * FROM ${processMap[processType]} WHERE batch_id = ?`,
        [batchId]
      );

      if (existingRecords.length > 0) {
        throw new Error(`批次 ${batchId} 已在${processType}环节被记录`);
      }

      // 检查流程顺序
      await this.validateProcessSequence(batchId, processType, connection);

      // 记录当前时间
      const now = moment().format('YYYY-MM-DD HH:mm:ss');

      // 插入记录，增加employee和company字段
      await connection.execute(
        `INSERT INTO ${processMap[processType]} (batch_id, timestamp, employee, company) VALUES (?, ?, ?, ?)`,
        [batchId, now, employee, company]
      );

      await connection.commit();
      return { success: true, message: `批次 ${batchId} 的${processType}操作已记录` };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 验证流程顺序
  async validateProcessSequence(batchId, currentProcess, connection) {
    const currentIndex = processSequence.indexOf(currentProcess);

    // 如果是第一个流程，不需要验证
    if (currentIndex === 0) return true;

    // 检查之前的流程是否都已完成
    const previousProcesses = processSequence.slice(0, currentIndex);
    const missingProcesses = [];

    for (const process of previousProcesses) {
      const [records] = await connection.execute(
        `SELECT * FROM ${processMap[process]} WHERE batch_id = ?`,
        [batchId]
      );

      if (records.length === 0) {
        missingProcesses.push(process);
      }
    }

    if (missingProcesses.length > 0) {
      const missingSteps = missingProcesses.map((process, index) => {
        return `${index + 1}.${process === 'storage' ? '扫码入库' :
                process === 'film' ? '扫码贴膜' :
                process === 'cutting' ? '扫码切割' :
                process === 'inspection' ? '扫码检验' : '扫码出货'}`;
      }).join(' ');

      throw new Error(`操作失败，请先完成以下步骤: ${missingSteps}`);
    }

    return true;
  }

  // 按时间范围查询数据
  async queryByTimeRange(processType, startTime, endTime) {
    if (!processMap[processType]) {
      throw new Error('无效的操作类型');
    }

    const [records] = await pool.execute(
      `SELECT id, batch_id, timestamp, employee, company FROM ${processMap[processType]}
       WHERE timestamp BETWEEN ? AND ?
       ORDER BY timestamp ASC`,
      [startTime, endTime]
    );

    // 在返回之前格式化所有时间戳为字符串，确保employee和company字段存在
    return records.map(record => ({
      ...record,
      timestamp: formatDateTime(record.timestamp),
      employee: record.employee || '未知',
      company: record.company || '未知'
    }));
  }

  // 获取所有流程的批次数据
  async getAllProcessData(batchId) {
    const result = {};

    for (const process of processSequence) {
      const [records] = await pool.execute(
        `SELECT id, batch_id, timestamp, employee, company FROM ${processMap[process]} WHERE batch_id = ?`,
        [batchId]
      );

      // 格式化时间戳
      if (records.length > 0) {
        const record = records[0];
        if (record.timestamp) {
          record.timestamp = formatDateTime(record.timestamp);
        }
        // 确保employee和company字段存在
        record.employee = record.employee || '未知';
        record.company = record.company || '未知';
        result[process] = record;
      } else {
        result[process] = null;
      }
    }

    return result;
  }

  // 根据批次ID和流程类型查询数据
  async queryByBatchAndProcess(batchId, processType) {
    if (!processMap[processType]) {
      throw new Error('无效的操作类型');
    }

    const [records] = await pool.execute(
      `SELECT id, batch_id, timestamp, employee, company FROM ${processMap[processType]}
       WHERE batch_id = ?`,
      [batchId]
    );

    // 在返回之前格式化所有时间戳为字符串，确保employee和company字段存在
    return records.map(record => ({
      ...record,
      timestamp: formatDateTime(record.timestamp),
      employee: record.employee || '未知',
      company: record.company || '未知'
    }));
  }

  // 查询指定类型的所有数据（按时间降序）
  async queryAllByType(processType) {
    if (!processMap[processType]) {
      throw new Error('无效的操作类型');
    }

    const [records] = await pool.execute(
      `SELECT id, batch_id, timestamp, employee, company FROM ${processMap[processType]}
       ORDER BY timestamp DESC`
    );

    // 在返回之前格式化所有时间戳为字符串，确保employee和company字段存在
    return records.map(record => ({
      ...record,
      timestamp: formatDateTime(record.timestamp),
      employee: record.employee || '未知',
      company: record.company || '未知'
    }));
  }

  // 更新所有记录的员工和公司信息
  async updateAllEmployeeAndCompany() {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      for (const processType of Object.keys(processMap)) {
        await connection.execute(
          `UPDATE ${processMap[processType]} SET employee = '系统用户', company = '系统记录' WHERE employee = '未知' OR employee IS NULL OR company = '未知' OR company IS NULL`
        );
        console.log(`已更新${processType}表的员工和公司信息`);
      }

      await connection.commit();
      return { success: true, message: "所有表的员工和公司信息已更新" };
    } catch (error) {
      await connection.rollback();
      console.error('更新员工和公司信息失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 更新批次信息
  async updateBatchInfo(batchId, updateData) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      const results = {};

      // 首先获取批次的所有流程数据，以确定哪些流程已经完成
      const [completedProcesses] = await connection.execute(
        `SELECT 'storage' as process_type FROM ${processMap['storage']} WHERE batch_id = ?
         UNION ALL
         SELECT 'film' as process_type FROM ${processMap['film']} WHERE batch_id = ?
         UNION ALL
         SELECT 'cutting' as process_type FROM ${processMap['cutting']} WHERE batch_id = ?
         UNION ALL
         SELECT 'inspection' as process_type FROM ${processMap['inspection']} WHERE batch_id = ?
         UNION ALL
         SELECT 'shipping' as process_type FROM ${processMap['shipping']} WHERE batch_id = ?`,
        [batchId, batchId, batchId, batchId, batchId]
      );

      // 如果没有完成的流程，返回错误
      if (completedProcesses.length === 0) {
        throw new Error(`批次 ${batchId} 没有任何流程数据`);
      }

      // 将完成的流程转换为集合，便于查找
      const completedProcessSet = new Set(completedProcesses.map(p => p.process_type));

      // 检查是否需要更新批次ID或客户代码
      const hasBatchIdOrCompanyUpdate = updateData.newBatchId || updateData.company;

      // 检查是否需要更新员工信息
      const hasEmployeeUpdate = updateData.employee && updateData.employee.trim() !== '';

      // 获取要更新员工信息的流程类型
      const processTypeForEmployee = updateData.processTypeForEmployee;

      // 逐个更新已完成的流程
      for (const processType of Object.keys(processMap)) {
        // 检查该流程是否已完成
        if (completedProcessSet.has(processType)) {
          const updateFields = [];
          const updateValues = [];

          // 构建更新字段
          // 如果需要更新员工信息，且当前流程类型与指定的流程类型相同
          if (hasEmployeeUpdate && processType === processTypeForEmployee) {
            updateFields.push('employee = ?');
            updateValues.push(updateData.employee);
            console.log(`将更新${processType}表中批次 ${batchId} 的员工信息为 ${updateData.employee}`);
          }

          // 如果需要更新客户代码，则对所有已完成的流程进行更新
          if (updateData.company) {
            updateFields.push('company = ?');
            updateValues.push(updateData.company);
          }

          // 如果要更新批次ID，需要特殊处理，对所有已完成的流程进行更新
          if (updateData.newBatchId) {
            updateFields.push('batch_id = ?');
            updateValues.push(updateData.newBatchId);
          }

          if (updateFields.length > 0) {
            // 添加原批次ID到更新值数组
            updateValues.push(batchId);

            // 执行更新
            const [updateResult] = await connection.execute(
              `UPDATE ${processMap[processType]} SET ${updateFields.join(', ')} WHERE batch_id = ?`,
              updateValues
            );

            results[processType] = {
              success: true,
              affectedRows: updateResult.affectedRows
            };

            if (hasEmployeeUpdate && processType === processTypeForEmployee) {
              console.log(`已更新${processType}表中批次 ${batchId} 的员工信息`);
            }

            if (hasBatchIdOrCompanyUpdate) {
              console.log(`已更新${processType}表中批次 ${batchId} 的批次ID或客户代码信息`);
            }
          }
        }
      }

      await connection.commit();
      return {
        success: true,
        message: `批次 ${batchId} 的信息已更新`,
        results,
        completedProcesses: Array.from(completedProcessSet)
      };
    } catch (error) {
      await connection.rollback();
      console.error('更新批次信息失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 删除批次信息
  async deleteBatchInfo(batchId) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      const results = {};

      // 首先获取批次的所有流程数据，以确定哪些流程已经完成
      const [completedProcesses] = await connection.execute(
        `SELECT 'storage' as process_type FROM ${processMap['storage']} WHERE batch_id = ?
         UNION ALL
         SELECT 'film' as process_type FROM ${processMap['film']} WHERE batch_id = ?
         UNION ALL
         SELECT 'cutting' as process_type FROM ${processMap['cutting']} WHERE batch_id = ?
         UNION ALL
         SELECT 'inspection' as process_type FROM ${processMap['inspection']} WHERE batch_id = ?
         UNION ALL
         SELECT 'shipping' as process_type FROM ${processMap['shipping']} WHERE batch_id = ?`,
        [batchId, batchId, batchId, batchId, batchId]
      );

      // 如果没有完成的流程，返回错误
      if (completedProcesses.length === 0) {
        throw new Error(`批次 ${batchId} 没有任何流程数据`);
      }

      // 将完成的流程转换为集合，便于查找
      const completedProcessSet = new Set(completedProcesses.map(p => p.process_type));

      // 逐个删除已完成的流程
      for (const processType of Object.keys(processMap)) {
        // 检查该流程是否已完成
        if (completedProcessSet.has(processType)) {
          // 执行真正的删除操作
          const [deleteResult] = await connection.execute(
            `DELETE FROM ${processMap[processType]} WHERE batch_id = ?`,
            [batchId]
          );

          results[processType] = {
            success: true,
            affectedRows: deleteResult.affectedRows
          };

          console.log(`已从${processType}表中删除批次 ${batchId} 的记录`);
        }
      }

      await connection.commit();
      return {
        success: true,
        message: `批次 ${batchId} 的记录已完全删除`,
        results,
        completedProcesses: Array.from(completedProcessSet)
      };
    } catch (error) {
      await connection.rollback();
      console.error('删除批次信息失败:', error);
      console.error('错误堆栈:', error.stack);
      console.error('批次ID:', batchId);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 删除客户代码相关的所有数据
  async deleteCompanyData(company) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      const results = {};
      let totalDeleted = 0;

      // 获取该公司在各个流程中的批次ID
      const batchIds = new Set();

      // 从各个流程表中查找该公司的批次ID
      // 如果公司代码为"未知"，也查找公司代码为空字符串的记录
      const isUnknown = company === '未知';

      for (const processType of Object.keys(processMap)) {
        let records;
        if (isUnknown) {
          // 如果是查找"未知"公司，同时查找空字符串的记录
          [records] = await connection.execute(
            `SELECT batch_id FROM ${processMap[processType]} WHERE company = '' OR company IS NULL`,
            []
          );
        } else {
          // 否则正常查找指定公司的记录
          [records] = await connection.execute(
            `SELECT batch_id FROM ${processMap[processType]} WHERE company = ?`,
            [company]
          );
        }

        records.forEach(record => {
          if (record.batch_id) {
            batchIds.add(record.batch_id);
          }
        });
      }

      // 如果没有找到任何批次，返回空结果而不是抛出错误
      if (batchIds.size === 0) {
        return {
          success: true,
          message: `客户代码 ${company} 没有任何批次数据`,
          results: {},
          batchIds: [],
          totalDeleted: 0
        };
      }

      // 逐个删除每个流程表中该公司的数据
      for (const processType of Object.keys(processMap)) {
        // 直接删除该公司的所有记录
        let deleteResult;
        if (isUnknown) {
          // 如果是删除"未知"公司，同时删除空字符串的记录
          [deleteResult] = await connection.execute(
            `DELETE FROM ${processMap[processType]} WHERE company = '' OR company IS NULL`,
            []
          );
        } else {
          // 否则正常删除指定公司的记录
          [deleteResult] = await connection.execute(
            `DELETE FROM ${processMap[processType]} WHERE company = ?`,
            [company]
          );
        }

        results[processType] = {
          success: true,
          affectedRows: deleteResult.affectedRows
        };

        totalDeleted += deleteResult.affectedRows;
        console.log(`已从${processType}表中删除客户代码 ${company} 的 ${deleteResult.affectedRows} 条记录`);
      }

      await connection.commit();
      return {
        success: true,
        message: `客户代码 ${company} 的所有数据已删除，共 ${totalDeleted} 条记录`,
        results,
        batchIds: Array.from(batchIds)
      };
    } catch (error) {
      await connection.rollback();
      console.error('删除客户代码数据失败:', error);
      console.error('错误堆栈:', error.stack);
      console.error('客户代码:', company);
      throw error;
    } finally {
      connection.release();
    }
  }
}

module.exports = new ProcessModel();
import React, { useState, useRef } from 'react';
import {
  Alert,
  Button,
  Empty,
  Skeleton,
  message,
  Modal,
  Table,
  Tag,
  Progress,
  Form,
  Input,
  Space,
  Select
} from 'antd';
import {
  ProTable,
  ProFormSelect,
  ProFormText,
  ProFormDateTimeRangePicker
} from '@ant-design/pro-components';
import { DownloadOutlined, SearchOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { PROCESS_TYPES, PROCESS_COLORS } from '../utils/constants';
import { getProcessData, exportProcessData, getBatchData, updateBatchInfo, deleteBatchInfo } from '../utils/api';
import * as XLSX from 'xlsx';

const ExportPanel = () => {
  const proTableRef = useRef();
  const actionRef = useRef(); // 添加 actionRef 用于获取 ProTable 的引用
  const [exporting, setExporting] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [searchError, setSearchError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState('timestamp');
  const [sortOrder, setSortOrder] = useState('ascend'); // 默认按操作时间升序排列

  // 批次流程查询相关状态
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [selectedBatchId, setSelectedBatchId] = useState('');
  const [batchProcessData, setBatchProcessData] = useState(null);
  const [batchDataLoading, setBatchDataLoading] = useState(false);
  const [batchIdInput, setBatchIdInput] = useState('');  // 跟踪批次ID输入框的值

  // 修改功能相关状态
  const [editMode, setEditMode] = useState(false);
  const [editForm] = Form.useForm();
  const [updating, setUpdating] = useState(false);

  // 禁用未来日期
  const disabledDate = (current) => {
    return current && current > dayjs().endOf('day');
  };

  // 处理表格变化（排序、筛选、分页等）
  const handleTableChange = (pagination, filters, sorter) => {
    console.log('Table change:', { pagination, filters, sorter });

    // 处理排序
    if (sorter && sorter.field) {
      console.log('Setting sort:', sorter.field, sorter.order);
      setSortField(sorter.field);
      setSortOrder(sorter.order || 'ascend');
    }

    // 处理分页
    if (pagination) {
      setCurrentPage(pagination.current);
      setPageSize(pagination.pageSize);
    }
  };

  // 根据排序字段和排序方向对数据进行排序
  const getSortedData = () => {
    console.log('Getting sorted data with:', { sortField, sortOrder, resultsLength: searchResults.length });

    if (!sortField || !sortOrder || !searchResults.length) {
      return searchResults;
    }

    return [...searchResults].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // 处理时间字段
      if (sortField === 'timestamp') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (sortOrder === 'ascend') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  // 渲染流程状态的函数
  const renderProcessStatus = (record, processType) => {
    if (!record) {
      return <Tag color="default">未开始</Tag>;
    }

    return (
      <div>
        <Tag color={PROCESS_COLORS[processType]} style={{ marginBottom: '4px' }}>已完成</Tag>
        <div style={{ fontSize: '12px', color: '#666' }}>
          <div>{record.timestamp}</div>
          <div>{record.employee}</div>
        </div>
      </div>
    );
  };

  // 获取批次流程数据
  const fetchBatchProcessData = async (batchId) => {
    if (!batchId) {
      message.warning('请输入批次ID');
      return;
    }

    setBatchDataLoading(true);
    setSelectedBatchId(batchId);
    setEditMode(false); // 重置编辑模式

    try {
      const response = await getBatchData(batchId);

      if (response.data && response.data.success) {
        const batchData = response.data.data;

        // 检查是否有任何流程数据
        const hasAnyProcessData = Object.values(batchData).some(value => value !== null);

        if (hasAnyProcessData) {
          // 计算进度
          const completedSteps = Object.keys(batchData).filter(processType => batchData[processType] !== null).length;
          const progress = Math.round((completedSteps / 5) * 100);

          // 格式化数据为表格需要的格式
          const formattedData = {
            key: batchId,
            batch_id: batchId,
            storage: batchData.storage,
            film: batchData.film,
            cutting: batchData.cutting,
            inspection: batchData.inspection,
            shipping: batchData.shipping,
            progress: progress
          };

          setBatchProcessData(formattedData);
        } else {
          setBatchProcessData(null);
          message.info(`批次 ${batchId} 暂无流程数据`);
        }

        setBatchModalVisible(true);
      } else {
        message.error('获取批次流程数据失败');
      }
    } catch (error) {
      console.error('获取批次流程数据失败:', error);
      message.error('获取批次流程数据失败，请稍后重试');
    } finally {
      setBatchDataLoading(false);
    }
  };

  // 切换到编辑模式
  const handleEditMode = () => {
    if (!batchProcessData) return;

    // 预填充表单
    editForm.setFieldsValue({
      newBatchId: '',
      employee: '',
      company: batchProcessData.storage?.company || batchProcessData.film?.company || batchProcessData.cutting?.company || '',
      processTypeForEmployee: null // 初始化为空，要求用户选择
    });

    setEditMode(true);
  };

  // 取消编辑模式
  const handleCancelEdit = () => {
    setEditMode(false);
    editForm.resetFields();
  };

  // 显示删除确认对话框
  const showDeleteConfirm = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此批次的员工和客户代码信息吗？此操作不可恢复。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: handleDelete,
    });
  };

  // 处理删除
  const handleDelete = async () => {
    try {
      setUpdating(true);
      const response = await deleteBatchInfo(selectedBatchId);

      if (response.data && response.data.success) {
        message.success('批次信息删除成功');
        setBatchModalVisible(false);

        // 刷新表格数据
        try {
          if (actionRef.current && typeof actionRef.current.reload === 'function') {
            actionRef.current.reload();
          }
        } catch (error) {
          console.error('刷新表格数据失败:', error);
        }
      } else {
        message.error('删除失败: ' + (response.data?.message || '未知错误'));
      }
    } catch (error) {
      console.error('删除批次信息失败:', error);
      message.error('删除失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setUpdating(false);
    }
  };

  // 提交修改
  const handleSubmitEdit = async () => {
    try {
      const values = await editForm.validateFields();
      setUpdating(true);

      // 构建要更新的数据
      const updateData = {};

      if (values.newBatchId && values.newBatchId.trim() !== '') {
        updateData.newBatchId = values.newBatchId.trim();
      }

      if (values.employee && values.employee.trim() !== '') {
        updateData.employee = values.employee.trim();

        // 如果提供了员工信息，必须指定流程类型
        if (!values.processTypeForEmployee) {
          message.warning('更新员工信息时需要选择流程步骤');
          setUpdating(false);
          return;
        }

        // 将流程类型添加到更新数据中
        updateData.processTypeForEmployee = values.processTypeForEmployee;
      }

      if (values.company && values.company.trim() !== '') {
        updateData.company = values.company.trim();
      }

      if (Object.keys(updateData).length === 0) {
        message.warning('未检测到任何修改');
        return;
      }

      // 发送更新请求
      const response = await updateBatchInfo(selectedBatchId, updateData);

      if (response.data && response.data.success) {
        message.success('批次信息修改成功');
        setEditMode(false);
        editForm.resetFields();

        // 重新获取批次数据
        await fetchBatchProcessData(selectedBatchId);

        // 刷新表格数据
        try {
          if (actionRef.current && typeof actionRef.current.reload === 'function') {
            actionRef.current.reload();
          }
        } catch (error) {
          console.error('刷新表格数据失败:', error);
          // 不显示错误消息给用户，因为修改已经成功
        }
      } else {
        message.error('修改失败: ' + (response.data?.message || '未知错误'));
      }
    } catch (error) {
      console.error('修改批次信息失败:', error);
      message.error('修改失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setUpdating(false);
    }
  };

  // 将当前表格数据导出为Excel
  const exportTableDataToExcel = (data, processType) => {
    try {
      if (!data || data.length === 0) {
        message.warning('没有数据可导出');
        return;
      }

      // 创建Excel工作表
      const worksheet = XLSX.utils.json_to_sheet(data.map((item, index) => ({
        '序号': index + 1,
        '客户代码': item.company,
        '批次ID': item.batch_id,
        '操作时间': item.timestamp,
        '员工': item.employee
      })));

      // 设置列宽
      const wscols = [
        { wch: 10 }, // ID
        { wch: 25 }, // 客户代码
        { wch: 15 }, // 批次ID
        { wch: 25 }, // 操作时间
        { wch: 15 }  // 员工
      ];
      worksheet['!cols'] = wscols;

      // 创建Excel工作簿
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, `${processType}数据`);

      // 生成Excel文件并下载
      const filename = `${processType}_data_${new Date().getTime()}.xlsx`;
      XLSX.writeFile(workbook, filename);

      message.success('导出成功，文件已下载');
    } catch (error) {
      console.error('导出数据失败:', error);
      message.error('导出数据失败，请稍后重试');
    }
  };

  // 处理导出
  const handleExport = async (values) => {
    try {
      // 检查是否至少有一个筛选条件
      if (!values.batchId && !values.employee && !values.company && (!values.dateTimeRange || values.dateTimeRange.length !== 2)) {
        message.warning('请至少输入一个筛选条件(批次ID、员工、客户代码或时间范围)');
        return;
      }

      setExporting(true);

      try {
        // 直接导出当前表格中的数据（已经经过前端筛选）
        exportTableDataToExcel(searchResults, values.processType);
      } finally {
        setExporting(false);
      }
    } catch (error) {
      console.error('导出数据失败:', error);
      message.error('导出数据失败，请稍后重试');
      setExporting(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      render: (_, __, index) => index + 1,
    },
    {
      title: '批次ID',
      dataIndex: 'batch_id',
      key: 'batch_id',
      width: 120,
    },
    {
      title: '操作时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 170,
      valueType: 'dateTime',
      sorter: true,
      defaultSortOrder: 'ascend', // 默认升序排列
      sortDirections: ['ascend', 'descend', null], // 指定排序方向循环顺序
    },
    {
      title: '员工',
      dataIndex: 'employee',
      key: 'employee',
      width: 100,
    },
    {
      title: '客户代码',
      dataIndex: 'company',
      key: 'company',
      width: 120,
      ellipsis: true,
    },
  ];

  // 处理批次ID输入框值变化
  const handleBatchIdChange = (e) => {
    setBatchIdInput(e.target.value);
  };

  // 处理批次ID输入框回车事件
  const handleBatchIdKeyPress = (e) => {
    if (e.key === 'Enter' && batchIdInput) {
      fetchBatchProcessData(batchIdInput);
    }
  };

  // 搜索表单项
  const searchFormItems = [
    {
      title: '流程类型',
      dataIndex: 'processType',
      valueType: 'select',
      initialValue: 'storage',
      fieldProps: {
        options: PROCESS_TYPES.map(item => ({
          label: item.label,
          value: item.value,
        })),
      },
    },
    {
      title: '批次ID',
      dataIndex: 'batchId',
      valueType: 'text',
      fieldProps: {
        onKeyPress: handleBatchIdKeyPress,
        onChange: handleBatchIdChange,
        placeholder: '输入批次ID后可查看流程进度'
      },
    },
    {
      title: '员工',
      dataIndex: 'employee',
      valueType: 'text',
    },
    {
      title: '客户代码',
      dataIndex: 'company',
      valueType: 'text',
    },
    {
      title: '时间范围',
      dataIndex: 'dateTimeRange',
      valueType: 'dateTimeRange',
      fieldProps: {
        disabledDate,
      },
    },
  ];

  return (
    <>
      {searchError && (
        <Alert
          message="查询失败"
          description={searchError}
          type="error"
          showIcon
          closable
          style={{ marginBottom: '20px' }}
        />
      )}

      <ProTable
        headerTitle="数据筛选与导出"
        rowKey="id"
        columns={columns}
        dataSource={getSortedData()}
        onChange={handleTableChange}
        sortDirections={['ascend', 'descend', null]}
        sortOrder={sortOrder}
        sortField={sortField}
        actionRef={actionRef}
        style={{
          height: 'calc(100vh - 220px)', // 给表格设置固定高度
          display: 'flex',
          flexDirection: 'column'
        }}
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapseRender: false, // 禁用收起按钮
          span: {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 6,
          },
          columns: searchFormItems,
        }}
        form={{
          syncToUrl: false,
        }}
        options={{
          density: true,
          fullScreen: true,
          reload: false,
          setting: true,
        }}
        toolbar={{
          actions: [
            <Button
              key="viewBatchProcess"
              type="primary"
              icon={<SearchOutlined />}
              loading={batchDataLoading}
              onClick={() => {
                fetchBatchProcessData(batchIdInput);
              }}
              disabled={!batchIdInput}
              style={{ marginRight: '8px' }}
            >
              查看批次流程
            </Button>,
            <Button
              key="export"
              type="primary"
              icon={<DownloadOutlined />}
              loading={exporting}
              onClick={() => {
                const values = proTableRef.current?.getFieldsValue();
                handleExport(values);
              }}
              style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
            >
              导出Excel
            </Button>
          ],
        }}
        request={async (params) => {
          setSearchError('');

          try {
            // 检查是否至少有一个筛选条件
            if (!params.batchId && !params.employee && !params.company && (!params.dateTimeRange || params.dateTimeRange.length !== 2)) {
              message.warning('请至少输入一个筛选条件(批次ID、员工、客户代码或时间范围)');
              return {
                data: [],
                success: true,
                total: 0,
              };
            }

            // 设置时间范围
            let startTime = null;
            let endTime = null;

            // 如果设置了时间范围，则处理时间
            if (params.dateTimeRange && params.dateTimeRange.length === 2) {
              try {
                // 确保使用 dayjs 包装日期值
                const startDate = dayjs(params.dateTimeRange[0]);
                const endDate = dayjs(params.dateTimeRange[1]);

                if (startDate.isValid() && endDate.isValid()) {
                  startTime = startDate.format('YYYY-MM-DD HH:mm:ss');
                  endTime = endDate.format('YYYY-MM-DD HH:mm:ss');
                }
              } catch (error) {
                console.error('日期格式化错误:', error);
                message.warning('日期格式无效，请重新选择');
              }
            } else {
              // 如果没有设置时间范围，但有其他筛选条件，则设置一个很大的时间范围
              if (params.batchId || params.employee || params.company) {
                // 从很久以前（2000年）到现在
                startTime = '2000-01-01 00:00:00';
                endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
                console.log('使用默认时间范围:', { startTime, endTime });
              }
            }

            // 构建筛选条件
            const filters = {};
            if (params.batchId) {
              filters.batch_id = params.batchId.trim();
            }
            if (params.employee) {
              filters.employee = params.employee.trim();
            }
            if (params.company) {
              filters.company = params.company.trim();
            }

            // 调用后端API获取数据
            console.log('开始调用API获取数据:', {
              processType: params.processType,
              startTime,
              endTime,
              filters
            });
            const response = await getProcessData(params.processType, startTime, endTime, filters);
            console.log('API返回结果:', response);

            if (response.data && response.data.success) {
              let results = response.data.data;

              // 前端严格筛选逻辑
              if (params.batchId) {
                const batchIdTrim = params.batchId.trim();
                results = results.filter(item => item.batch_id === batchIdTrim);
              }

              if (params.employee) {
                const employeeTrim = params.employee.trim();
                results = results.filter(item => item.employee === employeeTrim);
              }

              if (params.company) {
                const companyTrim = params.company.trim();
                results = results.filter(item => item.company === companyTrim);
              }

              // 设置查询结果
              setSearchResults(results);

              if (results.length === 0) {
                message.info('没有找到符合条件的记录');
              } else {
                message.success(`查询成功，共找到 ${results.length} 条记录`);
              }

              return {
                data: results,
                success: true,
                total: results.length,
              };
            } else {
              setSearchError(response.data?.message || '查询失败');
              message.error(searchError);
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          } catch (error) {
            console.error('查询数据失败:', error);
            setSearchError(error.response?.data?.message || '查询数据失败，请稍后重试');
            message.error(searchError);
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columnsState={{
          persistenceKey: 'export-panel-table',
          persistenceType: 'localStorage',
        }}
        pagination={{
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 条记录`,
          pageSizeOptions: ['10', '20', '50', '100'],
          defaultPageSize: 10,
          position: ['bottomCenter'], // 分页组件位置固定在底部中间
          style: { marginBottom: '20px' } // 添加底部边距
        }}
        dateFormatter="string"
        formRef={proTableRef}
      />
      {/* 批次流程进展模态框 */}
      <Modal
        title={`${selectedBatchId} 批次流程进展`}
        open={batchModalVisible}
        onCancel={() => {
          setBatchModalVisible(false);
          setEditMode(false);
        }}
        footer={
          batchProcessData ? (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                {editMode ? (
                  <Space>
                    <Button onClick={handleCancelEdit}>取消</Button>
                    <Button type="primary" onClick={handleSubmitEdit} loading={updating}>提交修改</Button>
                  </Space>
                ) : (
                  <Space>
                    <Button
                      type="primary"
                      icon={<EditOutlined />}
                      onClick={handleEditMode}
                    >
                      修改批次信息
                    </Button>
                    <Button
                      type="primary"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={showDeleteConfirm}
                    >
                      删除批次信息
                    </Button>
                  </Space>
                )}
              </div>
              <Button onClick={() => setBatchModalVisible(false)}>关闭</Button>
            </div>
          ) : null
        }
        width={1000}
      >
        {batchProcessData ? (
          <>
            {editMode ? (
              <div style={{ marginBottom: '20px' }}>
                <Form
                  form={editForm}
                  layout="horizontal"
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Form.Item
                    label="新批次ID"
                    name="newBatchId"
                  >
                    <Input placeholder="输入新的批次ID (留空表示不修改)" allowClear />
                  </Form.Item>
                  <Form.Item
                    label="员工"
                    name="employee"
                  >
                    <Input placeholder="输入新的员工姓名 (留空表示不修改)" allowClear />
                  </Form.Item>

                  <Form.Item
                    label="员工所属流程"
                    name="processTypeForEmployee"
                    help="选择要修改员工信息的流程步骤，只有该步骤的员工信息会被修改"
                  >
                    <Select placeholder="选择要修改员工信息的流程步骤" allowClear>
                      {batchProcessData.storage && <Select.Option value="storage">入库</Select.Option>}
                      {batchProcessData.film && <Select.Option value="film">贴膜</Select.Option>}
                      {batchProcessData.cutting && <Select.Option value="cutting">切割</Select.Option>}
                      {batchProcessData.inspection && <Select.Option value="inspection">检验</Select.Option>}
                      {batchProcessData.shipping && <Select.Option value="shipping">出货</Select.Option>}
                    </Select>
                  </Form.Item>
                  <Form.Item
                    label="客户代码"
                    name="company"
                  >
                    <Input placeholder="输入新的客户代码 (留空表示不修改)" allowClear />
                  </Form.Item>
                </Form>
                <Alert
                  message="修改说明"
                  description={
                    <div>
                      <p><strong>批次ID和客户代码</strong>的修改将应用于批次已完成的所有流程步骤中，包括：</p>
                      <ul>
                        {batchProcessData.storage && <li>入库</li>}
                        {batchProcessData.film && <li>贴膜</li>}
                        {batchProcessData.cutting && <li>切割</li>}
                        {batchProcessData.inspection && <li>检验</li>}
                        {batchProcessData.shipping && <li>出货</li>}
                      </ul>
                      <p style={{ color: '#1890ff', marginTop: '10px' }}><strong>员工信息</strong>的修改只会应用于您选择的特定流程步骤，其他步骤的员工信息不受影响。</p>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </div>
            ) : null}
            <Table
              dataSource={[batchProcessData]}
              columns={[
                {
                  title: '批次ID',
                  dataIndex: 'batch_id',
                  key: 'batch_id',
                  width: 120,
                },
                {
                  title: '入库',
                  dataIndex: 'storage',
                  key: 'storage',
                  width: 150,
                  render: (record) => renderProcessStatus(record, 'storage'),
                },
                {
                  title: '贴膜',
                  dataIndex: 'film',
                  key: 'film',
                  width: 150,
                  render: (record) => renderProcessStatus(record, 'film'),
                },
                {
                  title: '切割',
                  dataIndex: 'cutting',
                  key: 'cutting',
                  width: 150,
                  render: (record) => renderProcessStatus(record, 'cutting'),
                },
                {
                  title: '检验',
                  dataIndex: 'inspection',
                  key: 'inspection',
                  width: 150,
                  render: (record) => renderProcessStatus(record, 'inspection'),
                },
                {
                  title: '出货',
                  dataIndex: 'shipping',
                  key: 'shipping',
                  width: 150,
                  render: (record) => renderProcessStatus(record, 'shipping'),
                },
                {
                  title: '完成进度',
                  dataIndex: 'progress',
                  key: 'progress',
                  width: 150,
                  render: (progress) => (
                    <Progress
                      percent={progress}
                      size="small"
                      status={progress === 100 ? 'success' : 'active'}
                    />
                  ),
                },
              ]}
              pagination={false}
            />
          </>
        ) : (
          <Empty
            description={
              <span>
                未找到批次 <strong>{selectedBatchId}</strong> 的流程数据，请确认批次ID是否正确
              </span>
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Modal>
    </>
  );
};

export default ExportPanel;

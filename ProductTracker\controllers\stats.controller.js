const processModel = require('../models/process.model'); // Adjust path if needed

class StatsController {
  // 获取公司完成率统计
  async getCompanyCompletionStats(req, res) {
    try {
      const rates = await processModel.calculateCompanyCompletionRates();
      return res.status(200).json({ success: true, data: rates });
    } catch (error) {
      console.error('Error in getCompanyCompletionStats controller:', error);
      return res.status(500).json({ success: false, message: '获取公司完成率统计失败' });
    }
  }
}

module.exports = new StatsController(); 
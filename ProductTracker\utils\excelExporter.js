const ExcelJS = require('exceljs');
const moment = require('moment');

class ExcelExporter {
  constructor() {
    this.workbook = new ExcelJS.Workbook();
    this.sheet = null;
  }

  // 创建并设置工作表
  initSheet(sheetName) {
    this.sheet = this.workbook.addWorksheet(sheetName);
    this.sheet.columns = [
      { header: '序号', key: 'id', width: 10 },
      { header: '批次', key: 'batchId', width: 15 },
      { header: '时间', key: 'timestamp', width: 25 },
      { header: '员工', key: 'employee', width: 15 },
      { header: '公司', key: 'company', width: 25 }
    ];

    // 设置表头样式
    this.sheet.getRow(1).font = { bold: true };
    this.sheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
  }

  // 添加数据
  addData(data) {
    if (!this.sheet) {
      throw new Error('工作表尚未初始化');
    }

    console.log('导出数据条数:', data.length);
    console.log('数据第一条示例:', data[0]);

    data.forEach(record => {
      // 格式化时间戳
      const formattedTimestamp = new Date(record.timestamp).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-');

      // 检查员工和公司字段
      const employee = record.employee || '未知';
      const company = record.company || '未知';

      console.log(`添加行: ID=${record.id}, 批次=${record.batch_id}, 时间=${formattedTimestamp}, 员工=${employee}, 公司=${company}`);

      this.sheet.addRow({
        id: record.id,
        batchId: record.batch_id,
        timestamp: formattedTimestamp,
        employee: employee,
        company: company
      });
    });

    // 设置所有单元格对齐方式
    for (let i = 2; i <= this.sheet.rowCount; i++) {
      this.sheet.getRow(i).alignment = { vertical: 'middle', horizontal: 'center' };
    }
  }

  // 生成Excel文件
  async generateExcel(filename) {
    if (!this.sheet) {
      throw new Error('工作表尚未初始化');
    }

    // 打印工作表的列信息
    console.log('工作表列定义:', this.sheet.columns);
    console.log('工作表行数:', this.sheet.rowCount);

    // 检查第一行数据
    if (this.sheet.rowCount > 1) {
      const firstRow = this.sheet.getRow(2);
      console.log('第一行数据单元格数:', firstRow.cellCount);
      console.log('第一行数据:', firstRow.values);
    }

    // 保存一份副本，以便检查
    const backupFilename = filename.replace('.xlsx', '_backup.xlsx');
    await this.workbook.xlsx.writeFile(backupFilename);
    console.log('备份文件已生成:', backupFilename);

    await this.workbook.xlsx.writeFile(filename);
    console.log('Excel文件已生成:', filename);
    return filename;
  }
}

module.exports = ExcelExporter;